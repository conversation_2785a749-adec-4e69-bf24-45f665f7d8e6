import { Action, createSelector } from '@ngrx/store';

import { AppState } from 'src/app/app.reducer';
import {
  dashboardActionTypes,
  FetchCPLTrackingSuccess,
  FetchCustomStatusCountSuccess,
  FetchDashboardCallReportSuccess,
  FetchDashboardCallsSuccess,
  FetchDashboardCountByStatusSuccess,
  FetchDashboardDataStatusSuccess,
  FetchDashboardLeadReportSuccess,
  FetchDashboardLeadsInContactSuccess,
  FetchDashboardLeadSourceSuccess,
  FetchDashboardLeadStatusSuccess,
  FetchDashboardLeadTrackerSuccess,
  FetchDashboardMeetingSuccess,
  FetchDashboardMeetingVisitCountSuccess,
  FetchDashboardPerformanceSuccess,
  FetchDashboardPipelineSuccess,
  FetchDashboardReceivedSuccess,
  FetchDashboardRemindersCountByDateSuccess,
  FetchDashboardSiteVisitSuccess,
  FetchDashboardSourceDataSuccess,
  FetchDashboardSourceDetailsSuccess,
  FetchDashboardSourceSuccess,
  FetchDashboardUpcomingEventsSuccess,
  FetchDashboardWhatsappSuccess,
  FetchLeadStatusTotalCountSuccess,
  FetchTopPerformerSuccess,
  UpdateCallReportFilterPayload,
  UpdateCPLFilterPayload,
  UpdateDashboardType,
  UpdateDashboardUsers,
  UpdateDashboardWithTeam,
  UpdateGlobalFilterPayload,
  UpdateLeadReportFilterPayload,
  UpdateMeetingFilterPayload,
  UpdatePerformanceFilterPayload,
  UpdatePipelineFilterPayload,
  UpdateReceivedFilterPayload,
  UpdateSource2FilterPayload,
  UpdateSourceFilterPayload,
  UpdateSVFilterPayload,
  UpdateTrackerFilterPayload,
} from 'src/app/reducers/dashboard/dashboard.actions';

export type DashboardState = {
  dashboardType: number;
  dashboardUsers: Array<String>;
  dashboardWithTeam: boolean;
  visitMeetingCount: {};
  countByStatus: {};
  leadReports: [];
  upcomingEvents: [];
  callReport: {};
  leadTracker: {};
  sourceData: {};
  leadSource: [];
  sourceDetails: [];
  pipeline: {};
  received: [];
  performance: [];
  leadsInContact: {};
  remindersCountByDate: [];
  meeting: [];
  siteVisit: [];
  countByStatusIsLoading: boolean;
  upcomingEventsIsLoading: boolean;
  remindersCountIsLoading: boolean;
  sourceIsLoading: boolean;
  sourceDetailsIsLoading: boolean;
  pipelineIsLoading: boolean;
  receivedIsLoading: boolean;
  leadReportsIsLoading: boolean;
  performanceIsLoading: boolean;
  callReportIsLoading: boolean;
  leadTrackerIsLoading: boolean;
  meetingIsLoading: boolean;
  siteVisitIsLoading: boolean;
  isTopPerformersLoading: boolean;
  topPerformerseList: {};
  sourceFiltersPayload: {};
  source2FiltersPayload: {};
  pipelineFiltersPayload: {};
  receivedFiltersPayload: {};
  callReportFiltersPayload: {};
  activityFiltersPayload: {};
  meetingFiltersPayload: {};
  SVFiltersPayload: {};
  leadReportFiltersPayload: {};
  performanceFiltersPayload: {};
  customStatusCount: {};
  customStatusCountIsLoading: boolean;
  sourceV1: [];
  sourceV1IsLoading: boolean;
  leadStatus: [];
  leadStatusIsLoading: boolean;
  leadStatusFiltersPayload: {};
  leadStatusTotalCount: number;
  dataStatus: [];
  dataStatusIsLoading: boolean;
  dataStatusFiltersPayload: {};
  calls: [];
  cplTracking: any[];
  callsIsLoading: boolean;
  cplIsLoading: boolean;
  callsFiltersPayload: {};
  cplFilterPayload: {},
  whatsapp: [];
  whatsappIsLoading: boolean;
  whatsappFiltersPayload: {};
  globalFiltersPayload: {
    FromDate?: string;
    ToDate?: string;
    DateType?: string | number;
    UserIds?: Array<string>;
    SearchText?: string;
    PageNumber?: number;
    PageSize?: number;
    IsWithTeam?: boolean;
    Projects?: Array<string>;
    LeadVisibility?: number;
    IsGenManagerWithTeam?: boolean;
    Designation?: Array<string>;
  };
};

const initialState: DashboardState = {
  dashboardType: 0,
  dashboardUsers: [],
  dashboardWithTeam: false,
  visitMeetingCount: {},
  countByStatus: {},
  leadReports: [],
  upcomingEvents: [],
  leadTracker: {},
  callReport: {},
  sourceData: {},
  leadSource: [],
  sourceDetails: [],
  pipeline: {},
  received: [],
  performance: [],
  meeting: [],
  siteVisit: [],
  leadsInContact: {},
  remindersCountByDate: [],
  countByStatusIsLoading: true,
  upcomingEventsIsLoading: true,
  remindersCountIsLoading: true,
  sourceIsLoading: true,
  sourceDetailsIsLoading: true,
  pipelineIsLoading: true,
  receivedIsLoading: true,
  leadReportsIsLoading: true,
  performanceIsLoading: true,
  callReportIsLoading: true,
  leadTrackerIsLoading: true,
  meetingIsLoading: true,
  siteVisitIsLoading: true,
  isTopPerformersLoading: true,
  topPerformerseList: {},
  sourceFiltersPayload: {},
  source2FiltersPayload: {},
  pipelineFiltersPayload: {},
  receivedFiltersPayload: {},
  callReportFiltersPayload: {},
  activityFiltersPayload: {},
  meetingFiltersPayload: {},
  SVFiltersPayload: {},
  leadReportFiltersPayload: {},
  performanceFiltersPayload: {},
  sourceV1: [],
  sourceV1IsLoading: true,
  leadStatus: [],
  leadStatusIsLoading: true,
  leadStatusFiltersPayload: {},
  leadStatusTotalCount: 0,
  dataStatus: [],
  dataStatusIsLoading: true,
  dataStatusFiltersPayload: {},
  customStatusCount: {},
  customStatusCountIsLoading: true,
  whatsapp: [],
  whatsappIsLoading: true,
  whatsappFiltersPayload: {},
  calls: [],
  cplTracking: [],
  callsIsLoading: true,
  cplIsLoading: true,
  callsFiltersPayload: {},
  cplFilterPayload: {},
  globalFiltersPayload: {
    FromDate: '',
    ToDate: '',
    DateType: 'All',
    UserIds: null,
    SearchText: '',
    PageNumber: 1,
    PageSize: 10,
    IsWithTeam: null,
    Projects: null,
    LeadVisibility: 0,
    IsGenManagerWithTeam: null,
    Designation: null,
  },
};

export function dashboardReducer(
  state: DashboardState = initialState,
  action: Action
): DashboardState {
  switch (action.type) {
    case dashboardActionTypes.FETCH_DASHBOARD_VISIT_MEETING_COUNT_SUCCESS:
      return {
        ...state,
        visitMeetingCount: (action as FetchDashboardMeetingVisitCountSuccess)
          .count,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DATA_SUCCESS:
      return {
        ...state,
        sourceData: (action as FetchDashboardSourceDataSuccess).source,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEADS_IN_CONTACT_SUCCESS:
      return {
        ...state,
        leadsInContact: (action as FetchDashboardLeadsInContactSuccess)
          .leadsInContact,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_COUNT_BY_STATUS:
      return {
        ...state,
        countByStatusIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_COUNT_BY_STATUS_SUCCESS:
      return {
        ...state,
        countByStatus: (action as FetchDashboardCountByStatusSuccess).dashboard,
        countByStatusIsLoading: false,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_REPORT:
      return {
        ...state,
        leadReportsIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_REPORT_SUCCESS:
      return {
        ...state,
        leadReports: (action as FetchDashboardLeadReportSuccess).leadReport,
        leadReportsIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_LEAD_REPORT_FILTER_PAYLOAD:
      return {
        ...state,
        leadReportFiltersPayload: (action as UpdateLeadReportFilterPayload)
          .filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_UPCOMING_EVENTS:
      return {
        ...state,
        upcomingEventsIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_UPCOMING_EVENTS_SUCCESS:
      return {
        ...state,
        upcomingEvents: (action as FetchDashboardUpcomingEventsSuccess)
          .upcomingEvents,
        upcomingEventsIsLoading: false,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_CALL_REPORT:
      return {
        ...state,
        callReportIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_CALL_REPORT_SUCCESS:
      return {
        ...state,
        callReport: (action as FetchDashboardCallReportSuccess).callReport,
        callReportIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_CALL_REPORT_FILTER_PAYLOAD:
      return {
        ...state,
        callReportFiltersPayload: (action as UpdateCallReportFilterPayload)
          .filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_TRACKER:
      return {
        ...state,
        leadTrackerIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_TRACKER_SUCCESS:
      return {
        ...state,
        leadTracker: (action as FetchDashboardLeadTrackerSuccess).leadTracker,
        leadTrackerIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_TRACKER_FILTER_PAYLOAD:
      return {
        ...state,
        activityFiltersPayload: (action as UpdateTrackerFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_REMINDERS_COUNT_BY_DATE:
      return {
        ...state,
        remindersCountIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_REMINDERS_COUNT_BY_DATE_SUCCESS:
      return {
        ...state,
        remindersCountByDate: (
          action as FetchDashboardRemindersCountByDateSuccess
        ).response,
        remindersCountIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_DASHBOARD_TYPE:
      return {
        ...state,
        dashboardType: (action as UpdateDashboardType).dashboardType,
      };
    case dashboardActionTypes.UPDATE_DASHBOARD_USERS:
      return {
        ...state,
        dashboardUsers: (action as UpdateDashboardUsers).dashboardUsers,
      };
    case dashboardActionTypes.UPDATE_DASHBOARD_WITH_TEAM:
      return {
        ...state,
        dashboardWithTeam: (action as UpdateDashboardWithTeam)
          .dashboardWithTeam,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_SOURCE:
      return {
        ...state,
        sourceIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_SOURCE_SUCCESS:
      return {
        ...state,
        leadSource: (action as FetchDashboardLeadSourceSuccess).source,
        sourceIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_SOURCE_FILTER_PAYLOAD:
      return {
        ...state,
        sourceFiltersPayload: (action as UpdateSourceFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DETAILS:
      return {
        ...state,
        sourceDetailsIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DETAILS_SUCCESS:
      return {
        ...state,
        sourceDetails: (action as FetchDashboardSourceDetailsSuccess).source,
        sourceDetailsIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_SOURCE2_FILTER_PAYLOAD:
      return {
        ...state,
        source2FiltersPayload: (action as UpdateSource2FilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_PIPELINE:
      return {
        ...state,
        pipelineIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_PIPELINE_SUCCESS:
      return {
        ...state,
        pipeline: (action as FetchDashboardPipelineSuccess).pipeline,
        pipelineIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_PIPELINE_FILTER_PAYLOAD:
      return {
        ...state,
        pipelineFiltersPayload: (action as UpdatePipelineFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_RECEIVED:
      return {
        ...state,
        receivedIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_RECEIVED_SUCCESS:
      return {
        ...state,
        received: (action as FetchDashboardReceivedSuccess).resp,
        receivedIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_RECEIVED_FILTER_PAYLOAD:
      return {
        ...state,
        receivedFiltersPayload: (action as UpdateReceivedFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_PERFORMANCE:
      return {
        ...state,
        performanceIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_PERFORMANCE_SUCCESS:
      return {
        ...state,
        performance: (action as FetchDashboardPerformanceSuccess).resp,
        performanceIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_PERFORMANCE_FILTER_PAYLOAD:
      return {
        ...state,
        performanceFiltersPayload: (action as UpdatePerformanceFilterPayload)
          .filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_MEETING:
      return {
        ...state,
        meetingIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_MEETING_SUCCESS:
      return {
        ...state,
        meeting: (action as FetchDashboardMeetingSuccess).resp,
        meetingIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_MEETING_FILTER_PAYLOAD:
      return {
        ...state,
        meetingFiltersPayload: (action as UpdateMeetingFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_SITE_VISIT:
      return {
        ...state,
        siteVisitIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_SITE_VISIT_SUCCESS:
      return {
        ...state,
        siteVisit: (action as FetchDashboardSiteVisitSuccess).resp,
        siteVisitIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_SV_FILTER_PAYLOAD:
      return {
        ...state,
        SVFiltersPayload: (action as UpdateSVFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_TOP_PERFORMER:
      return {
        ...state,
        isTopPerformersLoading: true,
      };
    case dashboardActionTypes.FETCH_TOP_PERFORMER_SUCCESS:
      return {
        ...state,
        topPerformerseList: (action as FetchTopPerformerSuccess).topPerformerse,
        isTopPerformersLoading: false,
      }
    case dashboardActionTypes.FETCH_CUSTOM_STATUS_COUNT:
      return {
        ...state,
        customStatusCountIsLoading: true,
      };
    case dashboardActionTypes.FETCH_CUSTOM_STATUS_COUNT_SUCCESS:
      return {
        ...state,
        customStatusCount: (action as FetchCustomStatusCountSuccess).dashboard,
        customStatusCountIsLoading: false,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_SOURCE:
      return {
        ...state,
        sourceV1IsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_SOURCE_SUCCESS:
      return {
        ...state,
        sourceV1: (action as FetchDashboardSourceSuccess).source,
        sourceV1IsLoading: false,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_STATUS:
      return {
        ...state,
        leadStatusIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_LEAD_STATUS_SUCCESS:
      return {
        ...state,
        leadStatus: (action as FetchDashboardLeadStatusSuccess).leadStatus,
        leadStatusIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_LEAD_STATUS_FILTER_PAYLOAD:
      return {
        ...state,
        leadStatusFiltersPayload: (action as UpdateSVFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_LEAD_STATUS_TOTAL_COUNT_SUCCESS:
      return {
        ...state,
        leadStatusTotalCount: (action as FetchLeadStatusTotalCountSuccess)
          .totalCount,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_DATA_STATUS:
      return {
        ...state,
        dataStatusIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_DATA_STATUS_SUCCESS:
      return {
        ...state,
        dataStatus: (action as FetchDashboardDataStatusSuccess).dataStatus,
        dataStatusIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_DATA_STATUS_FILTER_PAYLOAD:
      return {
        ...state,
        dataStatusFiltersPayload: (action as UpdateSVFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_CALLS:
      return {
        ...state,
        callsIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_CALLS_SUCCESS:
      return {
        ...state,
        calls: (action as FetchDashboardCallsSuccess).call,
        callsIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_CALLS_FILTER_PAYLOAD:
      return {
        ...state,
        callsFiltersPayload: (action as UpdateSVFilterPayload).filter,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_WHATSAPP:
      return {
        ...state,
        whatsappIsLoading: true,
      };
    case dashboardActionTypes.FETCH_DASHBOARD_WHATSAPP_SUCCESS:
      return {
        ...state,
        whatsapp: (action as FetchDashboardWhatsappSuccess).whatsapp,
        whatsappIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_WHATSAPP_FILTER_PAYLOAD:
      return {
        ...state,
        whatsappFiltersPayload: (action as UpdateSVFilterPayload).filter,
      };
    case dashboardActionTypes.UPDATE_GLOBAL_FILTER_PAYLOAD:
      const updatedFilters = {
        ...state.globalFiltersPayload,
        ...(action as UpdateGlobalFilterPayload).filter,
      };
      return {
        ...state,
        globalFiltersPayload: updatedFilters,
      };
    case dashboardActionTypes.FETCH_CPL_TRACKING:
      return {
        ...state,
        cplIsLoading: true,
      };
    case dashboardActionTypes.FETCH_CPL_TRACKING_SUCCESS:
      return {
        ...state,
        cplTracking: (action as FetchCPLTrackingSuccess).cpl,
        cplIsLoading: false,
      };
    case dashboardActionTypes.UPDATE_CPL_FILTER_PAYLOAD:
      return {
        ...state,
        cplFilterPayload: (action as UpdateCPLFilterPayload).filter,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.dashboard;

export const getDashboard = createSelector(
  selectFeature,
  (state: DashboardState) => state
);

export const getDashboardType = createSelector(
  selectFeature,
  (state: DashboardState) => state.dashboardType
);

export const getDashboardUsers = createSelector(
  selectFeature,
  (state: DashboardState) => state.dashboardUsers
);

export const getDashboardWithTeam = createSelector(
  selectFeature,
  (state: DashboardState) => state.dashboardWithTeam
);

export const getDashboardRemindersCountByDate = createSelector(
  selectFeature,
  (state: DashboardState) => state.remindersCountByDate
);

export const getCountByStatusIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.countByStatusIsLoading
);

export const getUpcomingEventsIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.upcomingEventsIsLoading
);

export const getRemindersCountIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.remindersCountIsLoading
);

export const getSourceIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.sourceIsLoading
);

export const getSourceDetailsIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.sourceDetailsIsLoading
);

export const getPipelineIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.pipelineIsLoading
);

export const getReceivedIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.receivedIsLoading
);

export const getLeadReportsIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.leadReportsIsLoading
);

export const getPerformanceIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.performanceIsLoading
);

export const getCallReportIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.callReportIsLoading
);

export const getLeadTrackerIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.leadTrackerIsLoading
);

export const getMeetingIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.meetingIsLoading
);

export const getSiteVisitIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.siteVisitIsLoading
);

export const getFiltersPayload = createSelector(
  selectFeature,
  (state: DashboardState) => {
    return {
      source: state.sourceFiltersPayload,
      source2: state.source2FiltersPayload,
      pipeline: state.pipelineFiltersPayload,
      received: state.receivedFiltersPayload,
      callReport: state.callReportFiltersPayload,
      activity: state.activityFiltersPayload,
      meeting: state.meetingFiltersPayload,
      siteVisit: state.SVFiltersPayload,
      leadReport: state.leadReportFiltersPayload,
      performance: state.performanceFiltersPayload,
      cplTracker: state.cplFilterPayload,
    };
  }
);

export const getIsTopPerformersLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.isTopPerformersLoading,
);

export const getTopPerformersList = createSelector(
  selectFeature,
  (state: DashboardState) => state.topPerformerseList,
);
export const getSourceV1 = createSelector(
  selectFeature,
  (state: DashboardState) => state.sourceV1
);

export const getLeadStatus = createSelector(
  selectFeature,
  (state: DashboardState) => state.leadStatus
);

export const getLeadStatusTotalCount = createSelector(
  selectFeature,
  (state: DashboardState) => state.leadStatusTotalCount
);

export const getCalls = createSelector(
  selectFeature,
  (state: DashboardState) => state.calls
);

export const getWhatsapp = createSelector(
  selectFeature,
  (state: DashboardState) => state.whatsapp
);

export const getDataStatus = createSelector(
  selectFeature,
  (state: DashboardState) => state.dataStatus
);

export const getCustomStatusCount = createSelector(
  selectFeature,
  (state: DashboardState) => state.customStatusCount
);

export const getSourceV1IsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.sourceV1IsLoading
);

export const getLeadsStatusIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.leadStatusIsLoading
);

export const getDataStatusIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.dataStatusIsLoading
);

export const getCustomStatusCountIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.customStatusCountIsLoading
);

export const getCallsIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.callsIsLoading
);

export const getWhatsappIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.whatsappIsLoading
);

export const getFiltersPayloadV1 = createSelector(
  selectFeature,
  (state: DashboardState) => {
    return {
      global: state.globalFiltersPayload,
      lead: state.leadStatusFiltersPayload,
      data: state.dataStatusFiltersPayload,
      call: state.callsFiltersPayload,
      whatsapp: state.whatsappFiltersPayload,
      cpl: state.cplFilterPayload,
    };
  }
);

export const getCplTrack = createSelector(
  selectFeature,
  (state: DashboardState) => state.cplTracking
);

export const getCplTrackIsLoading = createSelector(
  selectFeature,
  (state: DashboardState) => state.cplIsLoading
);

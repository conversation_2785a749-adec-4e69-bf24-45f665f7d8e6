import { Action } from '@ngrx/store';

export enum dashboardActionTypes {
  FETCH_DASHBOARD_COUNT_BY_STATUS = '[DASHBOARD] Fetch Dashboard Count By Status',
  FETCH_DASHBOARD_COUNT_BY_STATUS_SUCCESS = '[DASHBOARD] Fetch Dashboard Count By Status Success',

  FETCH_DASHBOARD_LEAD_REPORT = '[DASHBOARD] Fetch Dashboard Lead Report',
  FETCH_DASHBOARD_LEAD_REPORT_SUCCESS = '[DASHBOARD] Fetch Dashboard Lead Report Success',
  UPDATE_LEAD_REPORT_FILTER_PAYLOAD = '[DASHBOARD] Update Lead Report Filter Payload',

  FETCH_DASHBOARD_VISIT_MEETING_COUNT = '[DASHBOARD] Fetch Dashboard Visit Meeting Count',
  FETCH_DASHBOARD_VISIT_MEETING_COUNT_SUCCESS = '[DASHBOARD] Fetch Dashboard Visit Meeting Count Success',

  FETCH_DASHBOARD_UPCOMING_EVENTS = '[DASHBOARD] Fetch Dashboard Upcoming Events',
  FETCH_DASHBOARD_UPCOMING_EVENTS_SUCCESS = '[DASHBOARD] Fetch Dashboard Upcoming Events Success',

  FETCH_DASHBOARD_CALL_REPORT = '[DASHBOARD] Fetch Dashboard Lead Call Report',
  FETCH_DASHBOARD_CALL_REPORT_SUCCESS = '[DASHBOARD] Fetch Dashboard Lead Call Report Success',
  UPDATE_CALL_REPORT_FILTER_PAYLOAD = '[DASHBOARD] Update Lead Call Report Filter Payload',

  FETCH_DASHBOARD_LEAD_TRACKER = '[DASHBOARD] Fetch Dashboard Lead Tracker',
  FETCH_DASHBOARD_LEAD_TRACKER_SUCCESS = '[DASHBOARD] Fetch Dashboard Lead Tracker Success',
  UPDATE_TRACKER_FILTER_PAYLOAD = '[DASHBOARD] Update Lead Tracker Filter Payload',

  FETCH_DASHBOARD_LEADS_IN_CONTACT = '[DASHBOARD] Fetch Dashboard Leads In Contact',
  FETCH_DASHBOARD_LEADS_IN_CONTACT_SUCCESS = '[DASHBOARD] Fetch Dashboard Leads In Contact Success',

  FETCH_DASHBOARD_SOURCE_DATA = '[DASHBOARD] Fetch Dashboard Source Data',
  FETCH_DASHBOARD_SOURCE_DATA_SUCCESS = '[DASHBOARD] Fetch Dashboard Source Data Success',

  FETCH_DASHBOARD_REMINDERS_COUNT_BY_DATE = '[DASHBOARD] Fetch Dashboard Reminders count by date',
  FETCH_DASHBOARD_REMINDERS_COUNT_BY_DATE_SUCCESS = '[DASHBOARD] Fetch Dashboard Reminders count by date Success',

  UPDATE_DASHBOARD_TYPE = '[DASHBOARD] Update Dashboard Type',
  UPDATE_DASHBOARD_USERS = '[DASHBOARD] Update Dashboard Users',
  UPDATE_DASHBOARD_WITH_TEAM = '[DASHBOARD] Update Dashboard Users with Team',

  FETCH_DASHBOARD_LEAD_SOURCE = '[DASHBOARD] Fetch Dashboard Leads Source Data',
  FETCH_DASHBOARD_LEAD_SOURCE_SUCCESS = '[DASHBOARD] Fetch Dashboard Leads Source Data Success',
  UPDATE_SOURCE_FILTER_PAYLOAD = '[DASHBOARD] Update Source Filter Payload',

  FETCH_DASHBOARD_SOURCE_DETAILS = '[DASHBOARD] Fetch Dashboard Leads Source Details',
  FETCH_DASHBOARD_SOURCE_DETAILS_SUCCESS = '[DASHBOARD] Fetch Dashboard Leads Source Details Success',
  UPDATE_SOURCE2_FILTER_PAYLOAD = '[DASHBOARD] Update Source Details Filter Payload',

  FETCH_DASHBOARD_PIPELINE = '[DASHBOARD] Fetch Dashboard Leads Pipeline',
  FETCH_DASHBOARD_PIPELINE_SUCCESS = '[DASHBOARD] Fetch Dashboard Leads Pipeline Success',
  UPDATE_PIPELINE_FILTER_PAYLOAD = '[DASHBOARD] Update Pipeline Filter Payload',

  FETCH_DASHBOARD_RECEIVED = '[DASHBOARD] Fetch Dashboard Leads Received',
  FETCH_DASHBOARD_RECEIVED_SUCCESS = '[DASHBOARD] Fetch Dashboard Leads Received Success',
  UPDATE_RECEIVED_FILTER_PAYLOAD = '[DASHBOARD] Update Received Filter Payload',

  FETCH_DASHBOARD_PERFORMANCE = '[DASHBOARD] Fetch Dashboard Teams Performance',
  FETCH_DASHBOARD_PERFORMANCE_SUCCESS = '[DASHBOARD] Fetch Dashboard Teams Performance Success',
  UPDATE_PERFORMANCE_FILTER_PAYLOAD = '[DASHBOARD] Update Teams Performance Filter Payload',

  FETCH_DASHBOARD_MEETING = '[DASHBOARD] Fetch Dashboard Meeting Data',
  FETCH_DASHBOARD_MEETING_SUCCESS = '[DASHBOARD] Fetch Dashboard Meeting Data Success',
  UPDATE_MEETING_FILTER_PAYLOAD = '[DASHBOARD] Update Meeting Filter Payload',

  FETCH_DASHBOARD_SITE_VISIT = '[DASHBOARD] Fetch Dashboard Site Visit Data',
  FETCH_DASHBOARD_SITE_VISIT_SUCCESS = '[DASHBOARD] Fetch Dashboard Site Visit Data Success',
  UPDATE_SV_FILTER_PAYLOAD = '[DASHBOARD] Update Site Visit Filter Payload',
  FETCH_TOP_PERFORMER = '[DASHBOARD] Fetch Top Performer',
  FETCH_TOP_PERFORMER_SUCCESS = '[DASHBOARD] Fetch Top Performer Success',

  //custom status - hj
  FETCH_CUSTOM_STATUS_COUNT = '[DASHBOARD] Fetch Dashboard Custom Status Count',
  FETCH_CUSTOM_STATUS_COUNT_SUCCESS = '[DASHBOARD] Fetch Dashboard Custom Status Count Success',

  FETCH_DASHBOARD_SOURCE = '[DASHBOARD] Fetch Dashboard Source',
  FETCH_DASHBOARD_SOURCE_SUCCESS = '[DASHBOARD] Fetch Dashboard Source Success',

  FETCH_DASHBOARD_LEAD_STATUS = '[DASHBOARD] Fetch Dashboard Lead Status',
  FETCH_DASHBOARD_LEAD_STATUS_SUCCESS = '[DASHBOARD] Fetch Dashboard Lead Status Success',
  UPDATE_LEAD_STATUS_FILTER_PAYLOAD = '[DASHBOARD] Update Dashboard Lead Status Filter Payload',

  FETCH_LEAD_STATUS_TOTAL_COUNT = '[DASHBOARD] Fetch Dashboard Lead Status Total Count',
  FETCH_LEAD_STATUS_TOTAL_COUNT_SUCCESS = '[DASHBOARD] Fetch Dashboard Lead Status Total Count Success',

  FETCH_DASHBOARD_DATA_STATUS = '[DASHBOARD] Fetch Dashboard Data Status',
  FETCH_DASHBOARD_DATA_STATUS_SUCCESS = '[DASHBOARD] Fetch Dashboard Data Status Success',
  UPDATE_DATA_STATUS_FILTER_PAYLOAD = '[DASHBOARD] Update Dashboard Data Status Filter Payload',

  FETCH_DASHBOARD_CALLS = '[DASHBOARD] Fetch Dashboard Calls Report',
  FETCH_DASHBOARD_CALLS_SUCCESS = '[DASHBOARD] Fetch Dashboard Calls Report Success',
  UPDATE_CALLS_FILTER_PAYLOAD = '[DASHBOARD] Update Dashboard Calls Report Filter Payload',

  FETCH_DASHBOARD_WHATSAPP = '[DASHBOARD] Fetch Dashboard Whatsapp Report',
  FETCH_DASHBOARD_WHATSAPP_SUCCESS = '[DASHBOARD] Fetch Dashboard Whatsapp Report Success',
  UPDATE_WHATSAPP_FILTER_PAYLOAD = '[DASHBOARD] Update Dashboard Whatsapp Report Filter Payload',

  UPDATE_GLOBAL_FILTER_PAYLOAD = '[DASHBOARD] Update Global Filter Payload',

  FETCH_CPL_TRACKING = '[DASHBOARD] Fetch CPL Tracking',
  FETCH_CPL_TRACKING_SUCCESS = '[DASHBOARD] Fetch CPL Tracking Success',
  UPDATE_CPL_FILTER_PAYLOAD = '[DASHBOARD] Update Dashboard CPL Filter Payload',
}
export class FetchDashboardCountByStatus implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_COUNT_BY_STATUS;
  constructor(public payload: {}) { }
}

export class FetchDashboardCountByStatusSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_COUNT_BY_STATUS_SUCCESS;
  constructor(public dashboard = {}) { }
}

export class FetchDashboardLeadReport implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_LEAD_REPORT;
  constructor(public payload: {}) { }
}

export class FetchDashboardLeadReportSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_LEAD_REPORT_SUCCESS;
  constructor(public leadReport: any = []) { }
}

export class UpdateLeadReportFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_LEAD_REPORT_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardMeetingVisitCount implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_VISIT_MEETING_COUNT;
  constructor(public payload: {}) { }
}

export class FetchDashboardMeetingVisitCountSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_VISIT_MEETING_COUNT_SUCCESS;
  constructor(public count = {}) { }
}

export class FetchDashboardUpcomingEvents implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_UPCOMING_EVENTS;
  constructor(public payload: {}) { }
}

export class FetchDashboardUpcomingEventsSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_UPCOMING_EVENTS_SUCCESS;
  constructor(public upcomingEvents: any = []) { }
}

export class FetchDashboardCallReport implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_CALL_REPORT;
  constructor(public payload: {}) { }
}

export class FetchDashboardCallReportSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_CALL_REPORT_SUCCESS;
  constructor(public callReport = {}) { }
}

export class UpdateCallReportFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_CALL_REPORT_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardLeadTracker implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_LEAD_TRACKER;
  constructor(public payload: {}) { }
}

export class FetchDashboardLeadTrackerSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_LEAD_TRACKER_SUCCESS;
  constructor(public leadTracker = {}) { }
}

export class UpdateTrackerFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_TRACKER_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardLeadsInContact implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_LEADS_IN_CONTACT;
  constructor(public payload: {}) { }
}

export class FetchDashboardLeadsInContactSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_LEADS_IN_CONTACT_SUCCESS;
  constructor(public leadsInContact: any = {}) { }
}

export class FetchDashboardSourceData implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DATA;
  constructor(public payload: {}) { }
}

export class FetchDashboardSourceDataSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DATA_SUCCESS;
  constructor(public source: any = {}) { }
}

export class FetchDashboardRemindersCountByDate implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_REMINDERS_COUNT_BY_DATE;
  constructor(public payload: {}) { }
}

export class FetchDashboardRemindersCountByDateSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_REMINDERS_COUNT_BY_DATE_SUCCESS;
  constructor(public response: any = {}) { }
}

export class UpdateDashboardType implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_DASHBOARD_TYPE;
  constructor(public dashboardType: any = {}) { }
}

export class UpdateDashboardUsers implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_DASHBOARD_USERS;
  constructor(public dashboardUsers: Array<String> = []) { }
}

export class UpdateDashboardWithTeam implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_DASHBOARD_WITH_TEAM;
  constructor(public dashboardWithTeam: boolean) { }
}

export class FetchDashboardLeadSource implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_LEAD_SOURCE;
  constructor(public payload: {}) { }
}

export class FetchDashboardLeadSourceSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_LEAD_SOURCE_SUCCESS;
  constructor(public source: any = {}) { }
}

export class UpdateSourceFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_SOURCE_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardSourceDetails implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DETAILS;
  constructor(public payload: {}) { }
}

export class FetchDashboardSourceDetailsSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_SOURCE_DETAILS_SUCCESS;
  constructor(public source: any = {}) { }
}

export class UpdateSource2FilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_SOURCE2_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardPipeline implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_PIPELINE;
  constructor(public payload: {}) { }
}

export class FetchDashboardPipelineSuccess implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_PIPELINE_SUCCESS;
  constructor(public pipeline: any = {}) { }
}

export class UpdatePipelineFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_PIPELINE_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardReceived implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_RECEIVED;
  constructor(public payload: {}) { }
}

export class FetchDashboardReceivedSuccess implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_RECEIVED_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class UpdateReceivedFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_RECEIVED_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardPerformance implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_PERFORMANCE;
  constructor(public payload: {}) { }
}

export class FetchDashboardPerformanceSuccess implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_PERFORMANCE_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class UpdatePerformanceFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_PERFORMANCE_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardMeeting implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_MEETING;
  constructor(public payload: {}) { }
}

export class FetchDashboardMeetingSuccess implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_MEETING_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class UpdateMeetingFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_MEETING_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardSiteVisit implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_SITE_VISIT;
  constructor(public payload: {}) { }
}

export class FetchDashboardSiteVisitSuccess implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_SITE_VISIT_SUCCESS;
  constructor(public resp: any = {}) { }
}

export class UpdateSVFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_SV_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchTopPerformer implements Action {
  readonly type: string = dashboardActionTypes.FETCH_TOP_PERFORMER;
  constructor(public payload: any = []) { }
}

export class FetchTopPerformerSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_TOP_PERFORMER_SUCCESS;
  constructor(public topPerformerse: any = []) { }
}
export class FetchCustomStatusCount implements Action {
  readonly type: string = dashboardActionTypes.FETCH_CUSTOM_STATUS_COUNT;
  constructor() { }
}

export class FetchCustomStatusCountSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_CUSTOM_STATUS_COUNT_SUCCESS;
  constructor(public dashboard = {}) { }
}

export class FetchDashboardSource implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_SOURCE;
  constructor() { }
}

export class FetchDashboardSourceSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_SOURCE_SUCCESS;
  constructor(public source: any = {}) { }
}

export class FetchDashboardLeadStatus implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_LEAD_STATUS;
  constructor() { }
}

export class FetchDashboardLeadStatusSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_LEAD_STATUS_SUCCESS;
  constructor(public leadStatus: any = []) { }
}

export class UpdateLeadStatusFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_LEAD_STATUS_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchLeadStatusTotalCount implements Action {
  readonly type: string = dashboardActionTypes.FETCH_LEAD_STATUS_TOTAL_COUNT;
  constructor() { }
}

export class FetchLeadStatusTotalCountSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_LEAD_STATUS_TOTAL_COUNT_SUCCESS;
  constructor(public totalCount: any = []) { }
}

export class FetchDashboardDataStatus implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_DATA_STATUS;
  constructor() { }
}

export class FetchDashboardDataStatusSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_DATA_STATUS_SUCCESS;
  constructor(public dataStatus: any = []) { }
}

export class UpdateDataStatusFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_DATA_STATUS_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardCalls implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_CALLS;
  constructor() { }
}

export class FetchDashboardCallsSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_CALLS_SUCCESS;
  constructor(public call: any = []) { }
}

export class UpdateCallsFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_CALLS_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchDashboardWhatsapp implements Action {
  readonly type: string = dashboardActionTypes.FETCH_DASHBOARD_WHATSAPP;
  constructor() { }
}

export class FetchDashboardWhatsappSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_DASHBOARD_WHATSAPP_SUCCESS;
  constructor(public whatsapp: any = []) { }
}

export class UpdateWhatsappFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_WHATSAPP_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class UpdateGlobalFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_GLOBAL_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}

export class FetchCPLTracking implements Action {
  readonly type: string = dashboardActionTypes.FETCH_CPL_TRACKING;
  constructor(public payload: any) { }
}

export class FetchCPLTrackingSuccess implements Action {
  readonly type: string =
    dashboardActionTypes.FETCH_CPL_TRACKING_SUCCESS;
  constructor(public cpl: any = []) { }
}

export class UpdateCPLFilterPayload implements Action {
  readonly type: string = dashboardActionTypes.UPDATE_CPL_FILTER_PAYLOAD;
  constructor(public filter: any = {}) { }
}
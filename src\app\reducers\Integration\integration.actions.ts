import { Action } from '@ngrx/store';

export enum IntegrationActionTypes {
  FETCH_INTEGRATION_LIST = '[INTEGRATION] Fetch Integration List',
  FETCH_INTEGRATION_LIST_SUCCESS = '[INTEGRATION] Fetch Integration List Success',
  FETCH_INTEGRATION_BY_ID = '[INTEGRATION] Fetch Integration By Id',
  FETCH_INTEGRATION_BY_ID_SUCCESS = '[INTEGRATION] Fetch Integration By Id Success',
  FETCH_IVR_DETAILS_BY_ID = '[INTEGRATION] Fetch IVR Integration By Id',
  FETCH_IVR_DETAILS_BY_ID_SUCCESS = '[INTEGRATION] Fetch IVR Integration By Id Success',
  ADD_INTEGRATION = '[INTEGRATION] Add Integration',
  FETCH_INTEGRATION_SUBMITTED_ID = '[INTEGRATION] Fetch Integration Submitted Id',
  ADD_INTEGRATION_SUCCESS = '[INTEGRATION] Fetch Integration Success',
  DELETE_INTEGRATION = '[INTEGRATION] Delete Integration',
  FETCH_INTEGRATION_COUNT = '[INTEGRATION] Fetch  Integration Count',
  FETCH_INTEGRATION_COUNT_SUCCESS = '[INTEGRATION] Fetch  Integration Count Success',
  FETCH_IVR_INTEGRATION_COUNT = '[INTEGRATION] Fetch IVR Integration Count',
  FETCH_IVR_INTEGRATION_COUNT_SUCCESS = '[INTEGRATION] Fetch IVR Integration Count Success',
  GMAIL_INTEGRATION = '[INTEGRATION] Gmail Integration',
  FETCH_SUBSCRIBED_FORMS = '[INTEGRATION] Fetch Subscribed Forms',
  FETCH_SUBSCRIBED_FORMS_SUCCESS = '[INTEGRATION] Fetch Subscribed Forms Success',
  IVR_INTEGRATION = '[INTEGRATION] IVR Integration',
  IVR_INTEGRATION_SUCCESS = '[INTEGRATION] IVR Integration Success',
  COMMON_IVR_INTEGRATION = '[INTEGRATION] Common IVR Integration',
  COMMON_IVR_INTEGRATION_SUCCESS = '[INTEGRATION] Common IVR Integration Success',
  IVR_PRIMARY = '[INTEGRATION] IVR Integration Primary',
  UPDATE_IVR_ACCOUNT = '[INTEGRATION] Update IVR Account',
  // UPDATE_IVR_ACCOUNT_SUCCESS = '[INTEGRATION] Update IVR Account Success',
  FETCH_AGENTS_LIST = '[INTEGRATION] Fetch Agents',
  FETCH_AGENTS_LIST_SUCCESS = '[INTEGRATION] Fetch Agents Success',
  FETCH_VIRTUAL_NOS = '[INTEGRATION] Fetch IVR Virtual Numbers',
  FETCH_VIRTUAL_NOS_SUCCESS = '[INTEGRATION] Fetch IVR Virtual Numbers Success',
  FETCH_VIRTUAL_NO_ASSIGNMENT = '[INTEGRATION] Fetch IVR Virtual Number Assignment check',
  FETCH_VIRTUAL_NO_ASSIGNMENT_SUCCESS = '[INTEGRATION] Fetch IVR Virtual Number Assignment check Success',
  FETCH_IVR_SERVICE_PROVIDERS = '[INTEGRATION] Fetch IVR Service Providers',
  FETCH_IVR_SERVICE_PROVIDERS_SUCCESS = '[INTEGRATION] Fetch IVR Service Providers Success',
  CLICK_TO_CALL = '[INTEGRATION] Click To Call',
  COMMON_CLICK_TO_CALL = '[INTEGRATION] Common Click To Call',
  COMMON_IVR_CREATE = '[INTEGRATION] Common IVR Create',
  FETCH_FACEBOOK_ACCOUNTS = '[INTEGRATION] Fetch Facebook Accounts',
  FETCH_FACEBOOK_ACCOUNTS_SUCCESS = '[INTEGRATION] Fetch Facebook Accounts Success',
  ADD_FACEBOOK_ACCOUNT = '[INTEGRATION] Add Facebook Account',
  ADD_FACEBOOK_ACCOUNT_SUCCESS = '[INTEGRATION] Add Facebook Account Success',
  GOOGLE_ADS_LANDING_PAGE = '[INTEGRATION] Add Google Ads Landing Page',
  GOOGLE_ADS_LANDING_PAGE_SUCCESS = '[INTEGRATION] Add Google Ads Landing Page Success',
  GOOGLE_ADS_LEAD_FORM = '[INTEGRATION] Add Google Ads Lead Form',
  GOOGLE_ADS_LEAD_FORM_SUCCESS = '[INTEGRATION] Add Google Ads Lead Form Success',
  FETCH_GOOGLE_INTEGRATION_LIST = '[INTEGRATION] Fetch Google Integration List',
  FETCH_GOOGLE_INTEGRATION_LIST_SUCCESS = '[INTEGRATION] Fetch Google Integration List Success',
  DELETE_GOOGLE_INTEGRATION = '[INTEGRATION] Delete Google Integration',
  FETCH_GOOGLE_INTEGRATION_BY_ID = '[INTEGRATION] Fetch Google Integration By Id',
  FETCH_GOOGLE_INTEGRATION_BY_ID_SUCCESS = '[INTEGRATION] Fetch Google Integration By Id Success',
  DELETE_FACEBOOK_INTEGRATION = '[INTEGRATION] Delete Facebook Integration Account',
  AUTOMATE_INTEGRATION = '[INTEGRATION] Assign any lead coming from Integration to users',
  AUTOMATE_INTEGRATION_SUCCESS = '[INTEGRATION] Assign any lead coming from Integration to users Success',
  AUTOMATE_FB_AD_INTEGRATION = '[INTEGRATION] Assign any lead coming through FB Ad Integrations to users',
  AUTOMATE_FB_AD_INTEGRATION_SUCCESS = '[INTEGRATION] Assign any lead coming through FB Ad Integrations to users Success',
  AUTOMATE_FB_FORMS_INTEGRATION = '[INTEGRATION] Assign any lead coming through FB Forms Integrations to users',
  AUTOMATE_FB_FORMS_INTEGRATION_SUCCESS = '[INTEGRATION] Assign any lead coming through FB Forms Integrations to users Success',
  TOGGLE_fb_SUBSCRIPTION = '[INTEGRATION] Toggle subscription of FB ads',
  TOGGLE_fb_SUBSCRIPTION_SUCCESS = '[INTEGRATION] Toggle subscription of FB ads Success',
  SYNC_FB_ADS = '[INTEGRATION] Sync Ads of a FB account',
  SYNC_FB_ADS_SUCCESS = '[INTEGRATION] Sync Ads of a FB account success',
  FETCH_INTEGRATION_ASSIGNMENT_DETAILS = '[INTEGRATION] Get all assigned users for any Integration account',
  FETCH_INTEGRATION_ASSIGNMENT_DETAILS_SUCCESS = '[INTEGRATION] Get all assigned users for any Integration account Success',
  AGENCY_NAME = '[INTEGRATION] Agency Name',
  AGENCY_NAME_SUCCESS = '[INTEGRATION] Agency Name Success',
  UPDATE_PROJECT_LIST = '[INTEGRATION] Project List',
  UPDATE_PROJECT_LIST_SUCCESS = '[INTEGRATION] Project List Success',
  FETCH_AGENCY_NAME_LIST = '[INTEGRATION] Fetch Agency Name',
  FETCH_AGENCY_NAME_LIST_SUCCESS = '[INTEGRATION] Fetch Agency Name Success',
  FETCH_INTEGRATION_PROJECTS = '[INTEGRATION] Get all Projects for any Integration account',
  FETCH_INTEGRATION_PROJECTS_SUCCESS = '[INTEGRATION] Get all Projects for any Integration account Success',
  FETCH_FB_ACCOUNT_FORMS = '[INTEGRATION] Fetch Facebook Account Forms',
  FETCH_FB_ACCOUNT_FORMS_SUCCESS = '[INTEGRATION] Fetch Facebook Account Forms Success',
  ADD_JUST_LEAD = '[INTEGRATION] Add just lead account',
  UPDATE_JUST_LEAD = '[INTEGRATION] update just lead account',
  ADD_COMMON_FLOOR = '[INTEGRATION] Add Common Floor account',
  UPDATE_COMMON_FLOOR = '[INTEGRATION] update Common Floor account',
  FETCH_FB_BULK_LEADS = '[INTEGRATION] Fetch bulk leads from Facebook for all Forms and Ads',
  FETCH_FB_BULK_LEADS_SUCCESS = '[INTEGRATION] Fetch bulk leads from Facebook for all Forms and Ads success',
  ADD_MS_LEAD = '[INTEGRATION] Add Microsite Lead',
  ADD_MS_LEAD_SUCCESS = '[INTEGRATION] Add Microsite Lead Success',
  ADD_PROJECT_MS_LEAD = '[INTEGRATION] Add Project Microsite Lead',
  ADD_PROJECT_MS_LEAD_SUCCESS = '[INTEGRATION] Add Project Microsite Lead Success',
  FETCH_IVR_LIST = '[INTEGRATION] Fetch IVR List',
  FETCH_IVR_LIST_SUCCESS = '[INTEGRATION] Fetch IVR List Success',
  FETCH_EXPORT_FACEBOOK_STATUS = '[INTEGRATION] Fetch Export Status List',
  FETCH_EXPORT_FACEBOOK_STATUS_SUCCESS = '[INTEGRATION] Fetch Export Status List Success',
  ADD_WEBHOOK = '[INTEGRATION] Add Webhook account',
  ADD_WEBHOOK_SUCCESS = '[INTEGRATION] Add Webhook account Success',
  FETCH_WEBHOOK = '[INTEGRATION] Fetch Webhook',
  FETCH_WEBHOOK_SUCCESS = '[INTEGRATION] Fetch Webhook Success',
  FETCH_WEBHOOK_ACCOUNT = '[INTEGRATION] Fetch Webhook account',
  FETCH_WEBHOOK_ACCOUNT_SUCCESS = '[INTEGRATION] Fetch Webhook account Success',
  UPDATE_WEBHOOK = '[INTEGRATION] Update Webhook account',
  UPDATE_WEBHOOK_SUCCESS = '[INTEGRATION] Update Success Webhook account',
  ADD_PROPERTY_FINDER = '[INTEGRATION] Add Property Finder',
  UPDATE_PROPERTY_FINDER = '[INTEGRATION] Update Property Finder',
  UPDATE_BAYUT = '[INTEGRATION] Update Bayut',
  ADD_BAYUT = '[INTEGRATION] Add Bayut',
  DOES_ACCOUNTNAME_EXISTS = '[INTEGRATION] Exists Account Name',
  DOES_ACCOUNTNAME_EXISTS_SUCCESS = '[INTEGRATION] Exists Account Name Success',
  ADD_DUBIZZLE = '[INTEGRATION] Add Dubizzle',
  UPDATE_DUBIZZLE = '[INTEGRATION] Update Dubizzle',
  INTEGRATION_EMAIL = '[INTEGRATION] Integration Email',
  INTEGRATION_EMAIL_SUCCESS = '[INTEGRATION]  Integration Email Success',
  UPDATE_PIXEL_ACCOUNT = '[INTEGRATION] Update Pixel Account',
  UPDATE_PIXEL_ACCOUNT_SUCCESS = '[INTEGRATION] Update Pixel Account Success',
  ASSIGN_PAGE_SIZE_NUMBER = '[INTEGRATION] Assign Page Size and Page Number',
  FETCH_FACEBOOK_MARKETING = '[INTEGRATION] Fetch Facebook Marketing',
  FETCH_FACEBOOK_MARKETING_SUCCESS = '[INTEGRATION] Fetch Facebook Marketing Success',
  FETCH_FB_ACCOUNT = '[INTEGRATION] Fetch Facebook Account',
  FETCH_FB_ACCOUNT_SUCCESS = '[INTEGRATION] Fetch Facebook Account Success',
}
export class AddIntegration implements Action {
  readonly type: string = IntegrationActionTypes.ADD_INTEGRATION;
  constructor(public payload: any) { }
}
export class AddIntegrationSuccess implements Action {
  readonly type: string = IntegrationActionTypes.ADD_INTEGRATION_SUCCESS;
  constructor(public response: any) { }
}

export class IntegrationEmail implements Action {
  readonly type: string = IntegrationActionTypes.INTEGRATION_EMAIL;
  constructor(public payload: any) { }
}
export class IntegrationEmailSuccess implements Action {
  readonly type: string = IntegrationActionTypes.INTEGRATION_EMAIL_SUCCESS;
  constructor(public response: any) { }
}

export class FetchIntegrationById implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_INTEGRATION_BY_ID;
  constructor(public id: string) { }
}

export class FetchIntegrationByIdSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_INTEGRATION_BY_ID_SUCCESS;
  constructor(public response: any) { }
}

export class FetchGoogleIntegrationById implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_BY_ID;
  constructor(public id: string) { }
}
export class FetchGoogleIntegrationByIdSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_BY_ID_SUCCESS;
  constructor(public response: any) { }
}

export class FetchIntegrationCount implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_INTEGRATION_COUNT;
  constructor() { }
}

export class FetchIntegrationCountSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_INTEGRATION_COUNT_SUCCESS;
  constructor(public response: any) { }
}

export class FetchIVRIntegrationCount implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_IVR_INTEGRATION_COUNT;
  constructor() { }
}

export class FetchIVRIntegrationCountSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_IVR_INTEGRATION_COUNT_SUCCESS;
  constructor(public response: any) { }
}

export class FetchActiveIntegrationId implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_INTEGRATION_SUBMITTED_ID;
  constructor(public propertyId: String) { }
}
export class FetchIntegrationList implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_INTEGRATION_LIST;
  constructor(
    public payload?: any,
  ) { }
}
export class FetchIntegrationListSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_INTEGRATION_LIST_SUCCESS;
  constructor(public integrationData: any[]) { }
}
export class FetchIvrDetailsById implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_IVR_DETAILS_BY_ID;
  constructor(public id: string) { }
}
export class FetchIvrDetailsByIdSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_IVR_DETAILS_BY_ID_SUCCESS;
  constructor(public response: any) { }
}
export class DeleteIntegration implements Action {
  readonly type: string = IntegrationActionTypes.DELETE_INTEGRATION;
  constructor(public payload: any) { }
}

export class GmailIntegration implements Action {
  readonly type: string = IntegrationActionTypes.GMAIL_INTEGRATION;
  constructor(public resource: any) { }
}
export class FetchSubscribedForms implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_SUBSCRIBED_FORMS;
  constructor(public payload: any) { }
}

export class FetchSubscribedFormsSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_SUBSCRIBED_FORMS_SUCCESS;
  constructor(public response: any) { }
}

export class IvrIntegration implements Action {
  readonly type: string = IntegrationActionTypes.IVR_INTEGRATION;
  constructor(public payload: any) { }
}

export class IvrIntegrationSuccess implements Action {
  readonly type: string = IntegrationActionTypes.IVR_INTEGRATION_SUCCESS;
  constructor(public response: any) { }
}

export class CommonIvrIntegration implements Action {
  readonly type: string = IntegrationActionTypes.COMMON_IVR_INTEGRATION;
  constructor(public payload: any) { }
}

export class CommonIvrIntegrationSuccess implements Action {
  readonly type: string = IntegrationActionTypes.COMMON_IVR_INTEGRATION_SUCCESS;
  constructor(public response: any) { }
}

export class UpdateIVRAccount implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_IVR_ACCOUNT;
  constructor(public payload: any) { }
}

export class MakePrimary implements Action {
  readonly type: string = IntegrationActionTypes.IVR_PRIMARY;
  constructor(public id: any) { }
}

export class CommonIVRCreate implements Action {
  readonly type: string = IntegrationActionTypes.COMMON_IVR_CREATE;
  constructor(public payload: any) { }
}

export class CommonClickToCall implements Action {
  readonly type: string = IntegrationActionTypes.COMMON_CLICK_TO_CALL;
  constructor(public payload: any) { }
}

export class ClickToCall implements Action {
  readonly type: string = IntegrationActionTypes.CLICK_TO_CALL;
  constructor(public payload: any) { }
}

export class FetchAgents implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_AGENTS_LIST;
  constructor() { }
}

export class FetchAgentsSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_AGENTS_LIST_SUCCESS;
  constructor(public response: any) { }
}

export class FetchVirtualNos implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_VIRTUAL_NOS;
  constructor() { }
}

export class FetchVirtualNosSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_VIRTUAL_NOS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchVNAssignment implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_VIRTUAL_NO_ASSIGNMENT;
  constructor(public leadId: string) { }
}

export class FetchVNAssignmentSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_VIRTUAL_NO_ASSIGNMENT_SUCCESS;
  constructor(public response: any) { }
}

export class FetchIvrServiceProviders implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_IVR_SERVICE_PROVIDERS;
  constructor() { }
}

export class FetchIvrServiceProvidersSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_IVR_SERVICE_PROVIDERS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchFacebookAccounts implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FACEBOOK_ACCOUNTS;
  constructor(public payload: any) { }
}
export class FetchFacebookAccountsSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_FACEBOOK_ACCOUNTS_SUCCESS;
  constructor(public response: any) { }
}

export class AddFacebookAccount implements Action {
  readonly type: string = IntegrationActionTypes.ADD_FACEBOOK_ACCOUNT;
  constructor(public payload: any) { }
}
export class AddFacebookAccountSuccess implements Action {
  readonly type: string = IntegrationActionTypes.ADD_FACEBOOK_ACCOUNT_SUCCESS;
  constructor(public response: any) { }
}

export class AddIntegrationGoogleLanding implements Action {
  readonly type: string = IntegrationActionTypes.GOOGLE_ADS_LANDING_PAGE;
  constructor(public payload: any) { }
}
export class AddIntegrationGoogleLandingSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.GOOGLE_ADS_LANDING_PAGE_SUCCESS;
  constructor(public response: any) { }
}

export class AddIntegrationGoogleLead implements Action {
  readonly type: string = IntegrationActionTypes.GOOGLE_ADS_LEAD_FORM;
  constructor(public payload: any) { }
}
export class AddIntegrationGoogleLeadSuccess implements Action {
  readonly type: string = IntegrationActionTypes.GOOGLE_ADS_LEAD_FORM_SUCCESS;
  constructor(public response: any) { }
}

export class FetchGoogleIntegrationList implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_LIST;
  constructor(public payload: any) { }
}

export class FetchGoogleIntegrationListSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_LIST_SUCCESS;
  constructor(public response: any) { }
}

export class DeleteGoogleIntegration implements Action {
  readonly type: string = IntegrationActionTypes.DELETE_GOOGLE_INTEGRATION;
  constructor(public payload: any) { }
}

export class DeleteFacebookIntegration implements Action {
  readonly type: string = IntegrationActionTypes.DELETE_FACEBOOK_INTEGRATION;
  constructor(public id: string) { }
}

export class AutomateIntegration implements Action {
  readonly type: string = IntegrationActionTypes.AUTOMATE_INTEGRATION;
  constructor(public payload: any) { }
}

export class AutomateIntegrationSuccess implements Action {
  readonly type: string = IntegrationActionTypes.AUTOMATE_INTEGRATION_SUCCESS;
  constructor(public response: any) { }
}

export class AutomateFBAdIntegration implements Action {
  readonly type: string = IntegrationActionTypes.AUTOMATE_FB_AD_INTEGRATION;
  constructor(public payload: any) { }
}
export class AutomateFBAdIntegrationSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.AUTOMATE_FB_AD_INTEGRATION_SUCCESS;
  constructor(public response: any) { }
}

export class AutomateFBFormsIntegration implements Action {
  readonly type: string = IntegrationActionTypes.AUTOMATE_FB_FORMS_INTEGRATION;
  constructor(public payload: any) { }
}
export class AutomateFBFormsIntegrationSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.AUTOMATE_FB_FORMS_INTEGRATION_SUCCESS;
  constructor(public response: any) { }
}

export class ToggleFBSubscription implements Action {
  readonly type: string = IntegrationActionTypes.TOGGLE_fb_SUBSCRIPTION;
  constructor(public payload: any) { }
}

export class ToggleFBSubscriptionSuccess implements Action {
  readonly type: string = IntegrationActionTypes.TOGGLE_fb_SUBSCRIPTION_SUCCESS;
  constructor(public response: any) { }
}

export class SyncAdsOfAnFBAccount implements Action {
  readonly type: string = IntegrationActionTypes.SYNC_FB_ADS;
  constructor(public accountId: string) { }
}

export class SyncAdsOfAnFBAccountSuccess implements Action {
  readonly type: string = IntegrationActionTypes.SYNC_FB_ADS_SUCCESS;
  constructor(public response: any) { }
}

export class FetchIntegrationAssignmentDetails implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_INTEGRATION_ASSIGNMENT_DETAILS;
  constructor(public resource: any) { }
}

export class FetchIntegrationAssignmentDetailsSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_INTEGRATION_ASSIGNMENT_DETAILS_SUCCESS;
  constructor(public response: any) { }
}

export class AgencyName implements Action {
  readonly type: string = IntegrationActionTypes.AGENCY_NAME;
  constructor(public payload: any) { }
}

export class AgencyNameSuccess implements Action {
  readonly type: string = IntegrationActionTypes.AGENCY_NAME_SUCCESS;
  constructor(public response: any) { }
}

export class ProjectList implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_PROJECT_LIST;
  constructor(public payload: any) { }
}

export class ProjectListSuccess implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_PROJECT_LIST_SUCCESS;
  constructor(public response: any) { }
}
export class FetchAgencyNameList implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_AGENCY_NAME_LIST;
  constructor() { }
}

export class FetchAgencyNameListSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_AGENCY_NAME_LIST_SUCCESS;
  constructor(public response: any) { }
}
export class FetchIntegrationProjects implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_INTEGRATION_PROJECTS;
  constructor(public resource: any) { }
}

export class FetchIntegrationProjectsSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_INTEGRATION_PROJECTS_SUCCESS;
  constructor(public response: any) { }
}
export class FetchFbAccountForms implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FB_ACCOUNT_FORMS;
  constructor() { }
}
export class FetchFbAccountFormsSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FB_ACCOUNT_FORMS_SUCCESS;
  constructor(public response: any) { }
}
export class AddJustLead implements Action {
  readonly type: string = IntegrationActionTypes.ADD_JUST_LEAD;
  constructor(public payload: any) { }
}
export class UpdateJustLead implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_JUST_LEAD;
  constructor(public payload: any) { }
}
export class AddCommonFloor implements Action {
  readonly type: string = IntegrationActionTypes.ADD_COMMON_FLOOR;
  constructor(public payload: any) { }
}
export class UpdateCommonFloor implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_COMMON_FLOOR;
  constructor(public payload: any) { }
}
export class FetchBulkLeadsFromFb implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FB_BULK_LEADS;
  constructor(public payload: any) { }
}

export class FetchBulkLeadsFromFbSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FB_BULK_LEADS_SUCCESS;
  constructor(public response: any) { }
}

export class AddMicrositeLead implements Action {
  readonly type: string = IntegrationActionTypes.ADD_MS_LEAD;
  constructor(public payload: any) { }
}
export class AddMicrositeLeadSuccess implements Action {
  readonly type: string = IntegrationActionTypes.ADD_MS_LEAD_SUCCESS;
  constructor(public resp: string) { }
}

export class AddProjectMicrositeLead implements Action {
  readonly type: string = IntegrationActionTypes.ADD_PROJECT_MS_LEAD;
  constructor(public payload: any) { }
}
export class AddProjectMicrositeLeadSuccess implements Action {
  readonly type: string = IntegrationActionTypes.ADD_PROJECT_MS_LEAD_SUCCESS;
  constructor(public resp: string) { }
}

export class FetchIVRList implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_IVR_LIST;
  constructor() { }
}

export class FetchIVRListSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_IVR_LIST_SUCCESS;
  constructor(public resp: any) { }
}

export class FetchExportFacebookStatus implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_EXPORT_FACEBOOK_STATUS;
  constructor(public pageNumber: number, public pageSize: number) { }
}
export class FetchExportFacebookStatusSuccess implements Action {
  readonly type: string =
    IntegrationActionTypes.FETCH_EXPORT_FACEBOOK_STATUS_SUCCESS;
  constructor(public response: any[] = []) { }
}

export class AddWebhook implements Action {
  readonly type: string = IntegrationActionTypes.ADD_WEBHOOK;
  constructor(public payload: any) { }
}
export class AddWebhookSuccess implements Action {
  readonly type: string = IntegrationActionTypes.ADD_WEBHOOK_SUCCESS;
  constructor(public resp: string) { }
}

export class FetchWebhook implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_WEBHOOK;
  constructor() { }
}
export class FetchWebhookSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_WEBHOOK_SUCCESS;
  constructor(public resp: any) { }
}

export class AddPropertyFinder implements Action {
  readonly type: string = IntegrationActionTypes.ADD_PROPERTY_FINDER;
  constructor(public payload: any) { }
}

export class UpdatePropertyFinder implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_PROPERTY_FINDER;
  constructor(public payload: any) { }
}

export class AddBayut implements Action {
  readonly type: string = IntegrationActionTypes.ADD_BAYUT;
  constructor(public payload: any) { }
}

export class UpdateBayut implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_BAYUT;
  constructor(public payload: any) { }
}

export class AddDubizzle implements Action {
  readonly type: string = IntegrationActionTypes.ADD_DUBIZZLE;
  constructor(public payload: any) { }
}

export class UpdateDubizzle implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_DUBIZZLE;
  constructor(public payload: any) { }
}

export class UpdateWebhook implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_WEBHOOK;
  constructor(public payload: any) { }
}

export class FetchWebhookAccount implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_WEBHOOK_ACCOUNT;
  constructor(public id: string) { }
}

export class FetchWebhookAccountSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_WEBHOOK_ACCOUNT_SUCCESS;
  constructor(public response: any = {}) { }
}

export class DoesExistAccountName implements Action {
  readonly type: string = IntegrationActionTypes.DOES_ACCOUNTNAME_EXISTS;
  constructor(public accountName: string, public source: number) { }
}

export class DoesExistAccountNameSuccess implements Action {
  readonly type: string = IntegrationActionTypes.DOES_ACCOUNTNAME_EXISTS_SUCCESS;
  constructor(public response: boolean) { }
}

export class UpdatePixelAccount implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_PIXEL_ACCOUNT;
  constructor(public payload: any) { }
}

export class UpdatePixelAccountSuccess implements Action {
  readonly type: string = IntegrationActionTypes.UPDATE_PIXEL_ACCOUNT_SUCCESS;
  constructor(public response: any) { }
}

export class AssignPageSize implements Action {
  readonly type: string = IntegrationActionTypes.ASSIGN_PAGE_SIZE_NUMBER;
  constructor(public PageSize:number | null,public PageNumber:number | null) {}
}
export class FetchFacebookMarketing implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FACEBOOK_MARKETING;
  constructor(public payload: any) { }
}

export class FetchFacebookMarketingSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FACEBOOK_MARKETING_SUCCESS;
  constructor(public response: any) { }
}

export class FetchFbAccount implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FB_ACCOUNT;
  constructor() { }
}
export class FetchFbAccountSuccess implements Action {
  readonly type: string = IntegrationActionTypes.FETCH_FB_ACCOUNT_SUCCESS;
  constructor(public response: any) { }
}
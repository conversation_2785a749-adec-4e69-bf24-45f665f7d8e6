import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
import { getTenantName } from 'src/app/core/utils/common.util';

@Injectable({
  providedIn: 'root',
})
export class TenantService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.identityURL}api${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return '/tenants';
  }

  getTenantById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/${id}`);
  }

  getTenantAPIKey() {
    return this.http.get(`${this.serviceBaseUrl}/apikey?tenantId=${getTenantName()}&applicationName=AdminApp`);
  }
}

import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import {
  DomSanitizer,
  SafeResourceUrl,
  Title,
} from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getTenantName } from 'src/app/core/utils/common.util';
import { getUserProfile } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'this-applications',
  templateUrl: './this-applications.component.html',
})
export class ThisApplicationsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('iframeElement') iframeElement: ElementRef<HTMLIFrameElement>;
  safeUrl: SafeResourceUrl;
  applicationUrl: string = '';
  userDetails: any;
  tenantAPIKey: any;
  applicationDetails: any;
  isLoading: boolean = true;
  private currentAppId: string = '';
  private loadingTimeout: any;

  constructor(
    private store: Store<AppState>,
    private sanitizer: DomSanitizer,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    public shareDataService: ShareDataService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getUserProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.userDetails = item;
      });

    this.shareDataService.selectedApp$
      .pipe(takeUntil(this.stopper))
      .subscribe((app) => {
        if (app && app.id !== this.currentAppId) {
          console.log('App changed in this-applications:', app);

          // Clear any existing loading timeout
          if (this.loadingTimeout) {
            clearTimeout(this.loadingTimeout);
          }

          this.currentAppId = app.id;
          this.applicationDetails = app;
          this.applicationUrl = app?.product?.applicationUrl;
          this.metaTitle.setTitle(`CRM | ${app?.product?.description}`);
          this.headerTitle.setLangTitle(`${app?.product?.description}`);

          // Update safe URL when app changes
          this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
            this.applicationUrl
          );

          // Refresh iframe if it exists
          if (this.iframeElement?.nativeElement) {
            this.refreshIframe();
          }
        }
      });
  }

  ngAfterViewInit() {
    this.initIframe();
  }

  onIframeLoad() {
    console.log(
      'Iframe loaded for app:',
      this.applicationDetails?.product?.name
    );
    console.log('Tenant:', getTenantName());
    console.log('Admin:', localStorage.getItem('isAdmin'));
    console.log('Token:', localStorage.getItem('idToken'));
    console.log('Application Details:', this.applicationDetails);

    // Clear loading timeout if it exists
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    this.isLoading = false;

    if (this.iframeElement?.nativeElement && this.applicationDetails) {
      const iframe = this.iframeElement.nativeElement;

      iframe.contentWindow?.postMessage(
        {
          tenantId: getTenantName(),
          isAdmin: localStorage.getItem('isAdmin'),
          token: localStorage.getItem('idToken'),
          applicationDetails: this.applicationDetails,
          userDetails: JSON.parse(localStorage.getItem('userDetails') || '{}'),
        },
        '*'
      );
    }
  }

  initIframe() {
    const iframe = this.iframeElement?.nativeElement;
    if (iframe && this.applicationUrl) {
      this.isLoading = true;
      iframe.onload = () => {
        // Managed timeout for faster loading
        this.loadingTimeout = setTimeout(() => this.onIframeLoad(), 300);
      };

      // Set the src to trigger iframe load
      iframe.src = this.applicationUrl;
    }
  }

  refreshIframe() {
    const iframe = this.iframeElement?.nativeElement;
    if (iframe && this.applicationUrl) {
      console.log(
        'Refreshing iframe for new app:',
        this.applicationDetails?.product?.name
      );
      this.isLoading = true;

      // Optimized iframe refresh - direct src change for faster switching
      iframe.src = this.applicationUrl;

      // Add onload handler for the new src
      iframe.onload = () => {
        this.loadingTimeout = setTimeout(() => this.onIframeLoad(), 300);
      };
    }
  }

  ngOnDestroy() {
    // Clear any pending timeouts
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    this.stopper.next();
    this.stopper.complete();
  }
}

import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { Lead } from 'src/app/core/interfaces/leads.interface';
import {
  BulkReassignLeadSuccess,
  CommunicationBulkCountSuccess,
  CommunicationCountSuccess,
  ExcelUploadSuccess,
  FetchAdditionalPropertyListSuccess,
  FetchAdditionalPropertyValueSuccess,
  FetchAgencyNameListAnonymousSuccess,
  FetchAgencyNameListSuccess,
  FetchAllParentLeadByIdSuccess,
  FetchBulkOperationSuccess,
  FetchCampaignListAnonymousSuccess,
  FetchCampaignListSuccess,
  FetchChannelPartnerListAnonymousSuccess,
  FetchChannelPartnerListSuccess,
  FetchCountryBasedCitySuccess,
  FetchDuplicateFeatureSuccess,
  FetchExcelUploadedSuccess,
  FetchExportStatusSuccess,
  FetchLeadActiveCountSuccess,
  FetchLeadAppointmentsByProjectsSuccess,
  FetchLeadBaseFilterCountSuccess,
  FetchLeadCitiesSuccess,
  FetchLeadClusterNameSuccess,
  FetchLeadCommunitiesSuccess,
  FetchLeadCountriesSuccess,
  FetchLeadCurrencySuccess,
  FetchLeadCustomTopFiltersChildrenSuccess,
  FetchLeadCustomTopFiltersSuccess,
  FetchLeadDroppedCountSuccess,
  FetchLeadExportSuccess,
  FetchLeadFlagsCountSuccess,
  FetchLeadHistoryListSuccess,
  FetchLeadIdSuccess,
  FetchLeadLandLineSuccess,
  FetchLeadListSuccess,
  FetchLeadLocalitesSuccess,
  FetchLeadNationalitySuccess,
  FetchLeadNotInterestedCountSuccess,
  FetchLeadNotesListSuccess,
  FetchLeadPostalCodeSuccess,
  FetchLeadRotationSuccess,
  FetchLeadStatesSuccess,
  FetchLeadStatusCountSuccess,
  FetchLeadSubCommunitiesSuccess,
  FetchLeadTowerNamesSuccess,
  FetchLeadUnitNameSuccess,
  FetchLeadZonesSuccess,
  FetchLeadsCommunicationByIdsSuccess,
  FetchLocationsSuccess,
  FetchMatchingPropertyOrProjectListSuccess,
  FetchMigrateExcelUploadedSuccess,
  FetchProjectListSuccess,
  FetchPropertyListSuccess,
  FetchQRProjectListSuccess,
  FetchQRPropertyListSuccess,
  FetchSubSourceListSuccess,
  FetchUploadTypeNameListSuccess,
  GenerateOtpSuccess,
  HasLeadAltInfoSuccess,
  HasLeadInfoSuccess,
  LeadActionTypes,
  LeadExcelUploadSuccess,
  NavigateToLinkSuccess,
  SecondaryAssignLeadSuccess,
  UpdateCardData,
  UpdateFilterPayload,
  UpdateIsCardView,
  UpdateIsLoadMore,
  UpdateLeadCustomStatusEnabled,
  UpdateLeadSuccess,
  UploadLeadDocumentSuccess,
  addDuplicate,
  cancelAddLead,
  updateDuplicateAssignSuccess
} from 'src/app/reducers/lead/lead.actions';

export interface FetchResponse {
  items?: Lead[];
  itemsCount?: number;
  totalCount?: number;
  succeeded?: boolean;
  message?: string | null;
  errors?: string | null;
  data?: Lead;
}

export type LeadState = {
  leads?: Array<Lead>;
  success?: string;
  activeLead?: Lead;
  otpGeneratedId?: string;
  duplicateLeads?: string;
  totalCount?: number;
  itemsCount?: number;
  count?: {};
  baseFilterCount?: any;
  activeLeadsCount?: any;
  notInterestedLeadsCount?: Object;
  droppedLeadsCount?: Object;
  flagsCount?: Object;
  campaignListAnonymous?: any[];
  campaignListAnonymousIsLoading?: boolean;
  agencyNameListAnonymous?: any[];
  agencyNameListAnonymousIsLoading?: boolean;
  channelPartnerList?: any[];
  channelPartnerListIsLoading?: boolean;
  channelPartnerListAnonymous?: any[];
  channelPartnerListAnonymousIsLoading?: boolean;
  excelColumnHeading?: any;
  succeeded?: boolean;
  message?: any;
  errors?: any;
  data?: string;
  altData?: any;
  uploadDoc: any;
  projectList?: any;
  qrProjectList?: any;
  propertyList?: any;
  qrPropertyList?: any;
  excelUploadedList?: any;
  excelMigrateUploadedList?: any;
  exportStatus?: any;
  isExportStatusLoading: boolean;
  locations?: string[];
  cities?: string[];
  states?: string[];
  countries?: string[];
  subCommunities?: string[];
  communities?: string[];
  towerNames?: string[];
  countryBasedCity: any;
  countryBasedCityLoading: any;
  localites?: string[];
  zones?: string[];
  matchingPropertiesOrProjects?: any[];
  leadNotes?: any[];
  leadCurrency?: any[];
  subSourceList?: any;
  agencyNameList?: any;
  duplicateFeature?: any;
  alreadyAssignData?: any;
  bulkReassignDetails?: any;
  bulk2ndReassignDetails?: any;
  leadExport?: any;
  isLoading: boolean;
  subSourceListIsLoading: boolean;
  locationsIsLoading: boolean;
  citiesIsLoading: boolean;
  statesIsLoading: boolean;
  countriesIsLoading: boolean;
  subCommunitiesIsLoading: boolean;
  communitiesIsLoading: boolean;
  towerNamesIsLoading: boolean;
  localitesIsLoading: boolean;
  zonesIsLoading: boolean;
  projectListIsLoading: boolean;
  propertyListIsLoading: boolean;
  activeLeadIsLoading: boolean;
  leadStatusIsLoading: boolean;
  multipleLeadStatusIsLoading: boolean;
  reassignLeadIsLoading: boolean;
  reassignBothIsLoading: boolean;
  bulkReassignLeadIsLoading: boolean;
  bulk2ndReassignIsLoading: boolean;
  bulkSourceIsLoading: boolean;
  bulkProjectsIsLoading: boolean;
  bulkDeleteLeadsIsLoading: boolean;
  bulkRestoreLeadsIsLoading: boolean;
  statusCount: any;
  isLeadPosting: boolean;
  isLoadingHasLeadInfo: boolean;
  isLoadingHasLeadAltInfo: boolean;
  activeLeadAppointments: any[];
  activeLeadAppointmentsIsLoading: boolean;
  isMeetingOrVisitDoneLoading: boolean;
  isProjectAddLoading: boolean;
  leadsCommunication: Record<string, any>;
  leadsCommunicationIsLoading: boolean;
  leadNotesIsLoading: boolean;
  currencyListLoading: boolean;
  leadRotation: any[];
  leadRotationIsLoading: boolean;
  isCustomStatusEnabled: boolean;
  customTopFilters: any;
  customTopFiltersChildren: any;
  isCustomTopFiltersLoading: boolean;
  isCustomTopFiltersChildrenLoading: boolean;
  isExcelUploadedListLoading: boolean;
  isMigrateExcelUploadedListLoading: boolean;
  isAddLeadCancelled: boolean;
  isDuplicate: boolean;
  duplicateId: any;
  bulkOperation?: any;
  isBulkOperationLoading: boolean;
  isMatchingPropertyLoading: boolean;
  uploadTypeListIsLoading: boolean;
  uploadtypeNameList?: any;
  isMatchingPropertyOrProjectLoading: boolean;
  agencyNameListIsLoading: boolean;
  leadHistory?: any[];
  leadHistoryIsLoading: boolean;
  additionalPropertyIsLoading: boolean;
  additionalProperty?: any;
  additionalPropertyValueIsLoading: boolean;
  additionalPropertyValue?: any;
  filtersPayload: any;
  invoiceFiltersPayload: any;
  campaignIsLoading: boolean;
  campaignList?: any;
  cardData: any;
  isLoadMore: boolean;
  isCardView: boolean;
  leadNationality: any[];
  leadNationalityLoading: boolean;
  leadClusterName: any[];
  leadUnitName: any[];
  allParentLeadData: any[];
  leadByIdWithArchive: any
  leadClusterNameLoading: boolean;
  leadUnitNameLoading: boolean;
  isParentLeadDataLoading: boolean;
  isLeadByIdWithArchiveLoading: boolean;
  leadPostalCode: any[];
  leadPostalCodeLoading: boolean;
  leadLandLine?: any[];
  leadLandLineLoading?: boolean;
};

const initialState: LeadState = {
  leads: [],
  success: '',
  activeLead: {} as Lead,
  otpGeneratedId: '',
  duplicateLeads: '',
  totalCount: 0,
  itemsCount: 0,
  count: {},
  baseFilterCount: {
    isLoading: true,
  },
  activeLeadsCount: {
    isLoading: true,
  },
  notInterestedLeadsCount: {},
  droppedLeadsCount: {},
  statusCount: {
    isLoading: true,
  },
  flagsCount: {},
  excelColumnHeading: {},
  succeeded: false,
  message: {},
  errors: {},
  data: '',
  uploadDoc: [],
  projectList: [],
  qrProjectList: [],
  propertyList: [],
  qrPropertyList: [],
  excelUploadedList: [],
  excelMigrateUploadedList: [],
  exportStatus: [],
  isExportStatusLoading: true,
  leadNotes: [],
  leadCurrency: [],
  locations: [],
  cities: [],
  states: [],
  countries: [],
  subCommunities: [],
  communities: [],
  towerNames: [],
  countryBasedCity: {},
  countryBasedCityLoading: true,
  localites: [],
  zones: [],
  matchingPropertiesOrProjects: [],
  subSourceList: [],
  agencyNameList: [],
  duplicateFeature: [],
  alreadyAssignData: [],
  bulkReassignDetails: [],
  bulk2ndReassignDetails: [],
  leadExport: [],
  isLoading: true,
  subSourceListIsLoading: true,
  locationsIsLoading: true,
  statesIsLoading: true,
  countriesIsLoading: false,
  subCommunitiesIsLoading: false,
  communitiesIsLoading: false,
  towerNamesIsLoading: false,
  localitesIsLoading: true,
  citiesIsLoading: true,
  zonesIsLoading: true,
  projectListIsLoading: true,
  propertyListIsLoading: true,
  activeLeadIsLoading: true,
  leadStatusIsLoading: true,
  multipleLeadStatusIsLoading: true,
  reassignLeadIsLoading: true,
  reassignBothIsLoading: true,
  bulkReassignLeadIsLoading: true,
  bulk2ndReassignIsLoading: true,
  bulkSourceIsLoading: true,
  bulkProjectsIsLoading: true,
  bulkDeleteLeadsIsLoading: true,
  bulkRestoreLeadsIsLoading: true,
  isLeadPosting: false,
  isLoadingHasLeadInfo: false,
  isLoadingHasLeadAltInfo: false,
  activeLeadAppointments: [],
  activeLeadAppointmentsIsLoading: false,
  isMeetingOrVisitDoneLoading: false,
  isProjectAddLoading: false,
  leadsCommunication: {},
  leadsCommunicationIsLoading: false,
  leadNotesIsLoading: true,
  currencyListLoading: true,
  leadRotation: [],
  leadRotationIsLoading: true,
  customTopFiltersChildren: [],
  isCustomTopFiltersLoading: true,
  isCustomTopFiltersChildrenLoading: true,
  isExcelUploadedListLoading: true,
  isMigrateExcelUploadedListLoading: true,
  isAddLeadCancelled: false,
  duplicateId: '',
  isCustomStatusEnabled: false,
  customTopFilters: [],
  isDuplicate: false,
  bulkOperation: [],
  isBulkOperationLoading: true,
  isMatchingPropertyLoading: false,
  uploadTypeListIsLoading: true,
  uploadtypeNameList: [],
  isMatchingPropertyOrProjectLoading: false,
  agencyNameListIsLoading: true,
  leadHistory: [],
  leadHistoryIsLoading: true,
  additionalPropertyIsLoading: true,
  additionalProperty: [],
  additionalPropertyValueIsLoading: true,
  additionalPropertyValue: [],
  filtersPayload: {},
  invoiceFiltersPayload: {},
  campaignIsLoading: true,
  campaignList: [],
  cardData: [],
  isLoadMore: false,
  isCardView: false,
  leadNationality: [],
  leadNationalityLoading: true,
  leadClusterName: [],
  leadUnitName: [],
  allParentLeadData: [],
  leadByIdWithArchive: {},
  leadClusterNameLoading: true,
  leadUnitNameLoading: true,
  isParentLeadDataLoading: true,
  isLeadByIdWithArchiveLoading: true,
  leadPostalCodeLoading: true,
  leadPostalCode: [],
  campaignListAnonymous: [],
  campaignListAnonymousIsLoading: false,
  agencyNameListAnonymous: [],
  agencyNameListAnonymousIsLoading: false,
  channelPartnerList: [],
  channelPartnerListIsLoading: false,
  channelPartnerListAnonymous: [],
  channelPartnerListAnonymousIsLoading: false,
};

export function leadReducer(
  state: LeadState = initialState,
  action: Action
): LeadState {
  switch (action.type) {
    case LeadActionTypes.FETCH_LEAD_LIST:

      return {
        ...state,
        isLoading: true,
        leads: [],
        filtersPayload: {
          ...state?.filtersPayload,
          ...(state.isCardView && !state.isLoadMore ? { pageNumber: 1 } : {})
        },
        invoiceFiltersPayload: {
          ...state?.invoiceFiltersPayload,
          ...(state.isCardView && !state.isLoadMore ? { pageNumber: 1 } : {})
        },
      };
    case LeadActionTypes.UPDATE_FILTER_PAYLOAD:
      return {
        ...state,
        filtersPayload: {
          ...(action as UpdateFilterPayload).filter,
          ...(state.isCardView && !state.isLoadMore ? { pageNumber: 1 } : {})
        },
        isLoading: true,
      };
    case LeadActionTypes.UPDATE_INVOICE_FILTER:
      return {
        ...state,
        invoiceFiltersPayload: {
          ...(action as UpdateFilterPayload).filter,
          ...(state.isCardView && !state.isLoadMore ? { pageNumber: 1 } : {})
        },
        isLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_LIST_SUCCESS:
      const response = (action as FetchLeadListSuccess).response;
      return {
        ...state,
        leads: response?.items,
        totalCount: response?.totalCount,
        count: response?.data,
        isLoading: false,
      };
    case LeadActionTypes.UPDATE_CARD_DATA:
      return {
        ...state,
        cardData: [
          ...state.cardData, ...(action as UpdateCardData)?.data
        ]
      };
    case LeadActionTypes.CLEAR_CARD_DATA:
      return {
        ...state,
        cardData: []
      };
    case LeadActionTypes.UPDATE_IS_LOADMORE:
      return {
        ...state,
        isLoadMore: (action as UpdateIsLoadMore).isLoadMore
      };
    case LeadActionTypes.FETCH_LEAD_BY_ID:
      return {
        ...state,
        activeLeadIsLoading: true,
        activeLead: {} as Lead,
      };
    case LeadActionTypes.FETCH_LEAD_BY_ID_LIST_SUCCESS:
      return {
        ...state,
        activeLead: (action as FetchLeadIdSuccess).response,
        activeLeadIsLoading: false,
        leads: [
          ...state?.leads?.map((lead: any) =>
            (action as FetchLeadIdSuccess).response?.id == lead?.id
              ? (action as FetchLeadIdSuccess).response
              : lead
          ),
        ],
        cardData: [
          ...state?.cardData?.map((lead: any) =>
            (action as FetchLeadIdSuccess).response?.id == lead?.id
              ? (action as FetchLeadIdSuccess).response
              : lead
          ),
        ],
      };
    case LeadActionTypes.UPDATE_LEAD:
      return {
        ...state,
        isLoading: true,
        isLeadPosting: true,
      };
    case LeadActionTypes.UPDATE_LEAD_SUCCESS:
      return {
        ...state,
        success: (action as UpdateLeadSuccess).resp,
        isLoading: false,
        isLeadPosting: false,
      };
    case LeadActionTypes.UPDATE_LEAD_STATUS:
      return {
        ...state,
        leadStatusIsLoading: true,
      };
    case LeadActionTypes.UPDATE_LEAD_STATUS_SUCCESS:
      return {
        ...state,
        leadStatusIsLoading: false,
      };
    case LeadActionTypes.UPDATE_MULTIPLE_LEAD_STATUS:
      return {
        ...state,
        multipleLeadStatusIsLoading: true,
      };
    case LeadActionTypes.UPDATE_MULTIPLE_LEAD_STATUS_SUCCESS:
      return {
        ...state,
        multipleLeadStatusIsLoading: false,
      };
    case LeadActionTypes.ADD_LEAD:
      return {
        ...state,
        isLoading: true,
        isLeadPosting: true,
      };
    case LeadActionTypes.ADD_LEAD_SUCCESS:
      return {
        ...state,
        isLeadPosting: false,
      };
    case LeadActionTypes.GENERATE_STATUS_CHANGE_OTP_SUCCESS:
      return {
        ...state,
        otpGeneratedId: (action as GenerateOtpSuccess).id,
      };
    case LeadActionTypes.EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        duplicateLeads: (action as ExcelUploadSuccess).resp,
      };
    case LeadActionTypes.LEAD_EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        excelColumnHeading: (action as LeadExcelUploadSuccess).resp,
      };
    case LeadActionTypes.HAS_LEAD_INFO:
      return {
        ...state,
        data: (action as HasLeadInfoSuccess).resp,
        isLoadingHasLeadInfo: true,
      };
    case LeadActionTypes.HAS_LEAD_INFO_SUCCESS:
      return {
        ...state,
        data: (action as HasLeadInfoSuccess).resp,
        isLoadingHasLeadInfo: false,
      };
    case LeadActionTypes.HAS_LEAD_ALT_INFO:
      return {
        ...state,
        isLoadingHasLeadAltInfo: true,
      };
    case LeadActionTypes.HAS_LEAD_ALT_INFO_SUCCESS:
      return {
        ...state,
        altData: (action as HasLeadAltInfoSuccess).resp,
        isLoadingHasLeadAltInfo: false,
      };
    case LeadActionTypes.UPLOAD_DOCUMENT_SUCCESS:
      return {
        ...state,
        uploadDoc: action as UploadLeadDocumentSuccess,
      };
    case LeadActionTypes.FETCH_PROJECT_LIST:
      return {
        ...state,
        projectListIsLoading: true,
      };
    case LeadActionTypes.FETCH_PROJECT_LIST_SUCCESS:
      return {
        ...state,
        projectList: (action as FetchProjectListSuccess).response,
        projectListIsLoading: false,
      };
    case LeadActionTypes.FETCH_QR_PROJECT_LIST_SUCCESS:
      return {
        ...state,
        qrProjectList: (action as FetchQRProjectListSuccess).response,
      };
    case LeadActionTypes.FETCH_PROPERTY_LIST:
      return {
        ...state,
        propertyListIsLoading: true,
      };
    case LeadActionTypes.FETCH_PROPERTY_LIST_SUCCESS:
      return {
        ...state,
        propertyList: (action as FetchPropertyListSuccess).response,
        propertyListIsLoading: false,
      };
    case LeadActionTypes.FETCH_QR_PROPERTY_LIST_SUCCESS:
      return {
        ...state,
        qrPropertyList: (action as FetchQRPropertyListSuccess).response,
      };
    case LeadActionTypes.FETCH_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        isExcelUploadedListLoading: true,
      };
    case LeadActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        excelUploadedList: (action as FetchExcelUploadedSuccess).response,
        isExcelUploadedListLoading: false,
      };
    case LeadActionTypes.FETCH_EXPORT_STATUS:
      return {
        ...state,
        isExportStatusLoading: true,
      };
    case LeadActionTypes.FETCH_EXPORT_STATUS_SUCCESS:
      return {
        ...state,
        exportStatus: (action as FetchExportStatusSuccess).response,
        isExportStatusLoading: false,
      };
    case LeadActionTypes.FETCH_LOCATIONS:
      return {
        ...state,
        locationsIsLoading: true,
      };
    case LeadActionTypes.FETCH_LOCATIONS_SUCCESS:
      return {
        ...state,
        locations: (action as FetchLocationsSuccess).response,
        locationsIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_CITIES:
      return {
        ...state,
        citiesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_CITIES_SUCCESS:
      return {
        ...state,
        cities: (action as FetchLeadCitiesSuccess).response,
        citiesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_STATES:
      return {
        ...state,
        statesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_STATES_SUCCESS:
      return {
        ...state,
        states: (action as FetchLeadStatesSuccess).response,
        statesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_COUNTRIES:
      return {
        ...state,
        countriesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_COUNTRIES_SUCCESS:
      return {
        ...state,
        countries: (action as FetchLeadCountriesSuccess).response,
        countriesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_SUB_COMMUNITIES:
      return {
        ...state,
        subCommunitiesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_SUB_COMMUNITIES_SUCCESS:
      return {
        ...state,
        subCommunities: (action as FetchLeadSubCommunitiesSuccess).response,
        subCommunitiesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_COMMUNITIES:
      return {
        ...state,
        communitiesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_COMMUNITIES_SUCCESS:
      return {
        ...state,
        communities: (action as FetchLeadCommunitiesSuccess).response,
        communitiesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_TOWER_NAME:
      return {
        ...state,
        towerNamesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_TOWER_NAME_SUCCESS:
      return {
        ...state,
        towerNames: (action as FetchLeadTowerNamesSuccess).response,
        towerNamesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_LOCALITES:
      return {
        ...state,
        localitesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_LOCALITES_SUCCESS:
      return {
        ...state,
        localites: (action as FetchLeadLocalitesSuccess).response,
        localitesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_ZONES:
      return {
        ...state,
        zonesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_ZONES_SUCCESS:
      return {
        ...state,
        zones: (action as FetchLeadZonesSuccess).response,
        zonesIsLoading: false,
      };
    case LeadActionTypes.FETCH_MATCHING_PROPERTIES_OR_PROJECTS:
      return {
        ...state,
        isMatchingPropertyOrProjectLoading: true,
      };
    case LeadActionTypes.FETCH_MATCHING_PROPERTIES_OR_PROJECTS_LIST_SUCCESS:
      return {
        ...state,
        matchingPropertiesOrProjects:
          (action as FetchMatchingPropertyOrProjectListSuccess).response || [],
        isMatchingPropertyOrProjectLoading: false,
      };
    case LeadActionTypes.FETCH_SUB_SOURCE_LIST:
      return {
        ...state,
        subSourceListIsLoading: true,
      };
    case LeadActionTypes.FETCH_SUB_SOURCE_LIST_SUCCESS:
      return {
        ...state,
        subSourceList: (action as FetchSubSourceListSuccess).response,
        subSourceListIsLoading: false,
      };
    case LeadActionTypes.FETCH_AGENCY_NAME_LIST:
      return {
        ...state,
        agencyNameListIsLoading: true,
      };
    case LeadActionTypes.FETCH_AGENCY_NAME_LIST_SUCCESS:
      return {
        ...state,
        agencyNameList: (action as FetchAgencyNameListSuccess).response,
        agencyNameListIsLoading: false,
      };
    case LeadActionTypes.FETCH_AGENCY_NAME_LIST_ANONYMOUS:
      return {
        ...state,
        agencyNameListAnonymousIsLoading: true,
      };
    case LeadActionTypes.FETCH_AGENCY_NAME_LIST_ANONYMOUS_SUCCESS:
      return {
        ...state,
        agencyNameListAnonymous: (action as FetchAgencyNameListAnonymousSuccess).response,
        agencyNameListAnonymousIsLoading: false,
      };
    case LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST:
      return {
        ...state,
        channelPartnerListIsLoading: true,
      };
    case LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_SUCCESS:
      return {
        ...state,
        channelPartnerList: (action as FetchChannelPartnerListSuccess).response,
        channelPartnerListIsLoading: false,
      };
    case LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS:
      return {
        ...state,
        channelPartnerListAnonymousIsLoading: true,
      };
    case LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS_SUCCESS:
      return {
        ...state,
        channelPartnerListAnonymous: (action as FetchChannelPartnerListAnonymousSuccess).response,
        channelPartnerListAnonymousIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEADS_EXPORT_SUCCESS:
      return {
        ...state,
        leadExport: (action as FetchLeadExportSuccess).response,
      };
    case LeadActionTypes.FETCH_DUPLICATE_FEATURE_SUCCESS:
      return {
        ...state,
        duplicateFeature: (action as FetchDuplicateFeatureSuccess).response,
      };
    case LeadActionTypes.UPDATE_DUPLICATE_ASSIGNMENT_LIST_SUCCESS:
      return {
        ...state,
        alreadyAssignData: (action as updateDuplicateAssignSuccess).resp,
      };
    case LeadActionTypes.REASSIGN_LEAD:
      return {
        ...state,
        reassignLeadIsLoading: true,
      };
    case LeadActionTypes.REASSIGN_LEAD_SUCCESS:
      return {
        ...state,
        reassignLeadIsLoading: false,
      };
    case LeadActionTypes.REASSIGN_BOTH:
      return {
        ...state,
        reassignBothIsLoading: true,
      };
    case LeadActionTypes.REASSIGN_BOTH_SUCCESS:
      return {
        ...state,
        reassignBothIsLoading: false,
      };
    case LeadActionTypes.BULK_REASSIGN_LEAD:
      return {
        ...state,
        bulkReassignLeadIsLoading: true,
      };
    case LeadActionTypes.BULK_REASSIGN_LEAD_SUCCESS:
      return {
        ...state,
        bulkReassignDetails: (action as BulkReassignLeadSuccess).resp,
        bulkReassignLeadIsLoading: false,
      };
    case LeadActionTypes.SECONDARY_ASSIGN_LEAD:
      return {
        ...state,
        bulk2ndReassignIsLoading: true,
      };
    case LeadActionTypes.SECONDARY_ASSIGN_LEAD_SUCCESS:
      return {
        ...state,
        bulk2ndReassignDetails: (action as SecondaryAssignLeadSuccess).resp,
        bulk2ndReassignIsLoading: false,
      };
    case LeadActionTypes.FETCH_BASEFILTER_COUNT:
      return {
        ...state,
        baseFilterCount: {
          ...state.baseFilterCount,
          isLoading: true,
        },
      };
    case LeadActionTypes.FETCH_BASEFILTER_COUNT_SUCCESS:
      return {
        ...state,
        baseFilterCount: {
          ...(action as FetchLeadBaseFilterCountSuccess).response,
          isLoading: false,
        },
      };
    case LeadActionTypes.FETCH_ACTIVE_COUNT:
      return {
        ...state,
        activeLeadsCount: {
          ...state.activeLeadsCount,
          isLoading: true,
        },
      };
    case LeadActionTypes.FETCH_ACTIVE_COUNT_SUCCESS:
      return {
        ...state,
        activeLeadsCount: {
          ...(action as FetchLeadActiveCountSuccess).response,
          isLoading: false,
        },
      };
    case LeadActionTypes.FETCH_NOT_INTERESTED_COUNT_SUCCESS:
      return {
        ...state,
        notInterestedLeadsCount: (action as FetchLeadNotInterestedCountSuccess)
          .response,
      };
    case LeadActionTypes.FETCH_DROPPED_COUNT_SUCCESS:
      return {
        ...state,
        droppedLeadsCount: (action as FetchLeadDroppedCountSuccess).response,
      };
    case LeadActionTypes.FETCH_LEAD_STATUS_COUNT:
      return {
        ...state,
        statusCount: { counts: [], isLoading: true },
      };
    case LeadActionTypes.FETCH_LEAD_STATUS_COUNT_SUCCESS:
      return {
        ...state,
        statusCount: {
          counts: [...(action as FetchLeadStatusCountSuccess)?.response],
          isLoading: false,
        },
      };
    case LeadActionTypes.FETCH_FLAGS_COUNT_SUCCESS:
      return {
        ...state,
        flagsCount: (action as FetchLeadFlagsCountSuccess).response,
      };
    case LeadActionTypes.BULK_SOURCE:
      return {
        ...state,
        bulkSourceIsLoading: true,
      };
    case LeadActionTypes.BULK_SOURCE_SUCCESS:
      return {
        ...state,
        bulkSourceIsLoading: false,
      };
    case LeadActionTypes.BULK_PROJECTS:
      return {
        ...state,
        bulkProjectsIsLoading: true,
      };
    case LeadActionTypes.BULK_PROJECTS_SUCCESS:
      return {
        ...state,
        bulkProjectsIsLoading: false,
      };
    case LeadActionTypes.DELETE_LEADS:
      return {
        ...state,
        bulkDeleteLeadsIsLoading: true,
      };
    case LeadActionTypes.DELETE_LEADS_SUCCESS:
      return {
        ...state,
        bulkDeleteLeadsIsLoading: false,
      };
    case LeadActionTypes.RESTORE_LEADS:
      return {
        ...state,
        bulkRestoreLeadsIsLoading: true,
      };
    case LeadActionTypes.RESTORE_LEADS_SUCCESS:
      return {
        ...state,
        bulkRestoreLeadsIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_APPOINTMENTS_BY_PROJECTS:
      return {
        ...state,
        activeLeadAppointments: [],
        activeLeadAppointmentsIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_APPOINTMENTS_BY_PROJECTS_SUCCESS:
      return {
        ...state,
        activeLeadAppointments: (
          action as FetchLeadAppointmentsByProjectsSuccess
        )?.resp,
        activeLeadAppointmentsIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEADS_COMMUNICATION_BY_IDS:
      return {
        ...state,
        leadsCommunicationIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEADS_COMMUNICATION_BY_IDS_SUCCESS:
      return {
        ...state,
        leadsCommunication: {
          ...state?.leadsCommunication,
          ...(action as FetchLeadsCommunicationByIdsSuccess)?.resp,
        },
        leadsCommunicationIsLoading: false,
      };
    case LeadActionTypes.COMMUNICATION_BULK_COUNT_SUCCESS:
      const bulkInput: any = action as CommunicationBulkCountSuccess;
      const leadBulkCommunications: any = { ...state?.leadsCommunication };
      bulkInput?.payload?.ids?.forEach((input: any) => {
        leadBulkCommunications[input] = {
          WhatsApp:
            (leadBulkCommunications?.[input]?.WhatsApp || 0) +
            (bulkInput?.payload?.contactType === 0 ? 1 : 0),
          Call:
            (leadBulkCommunications?.[input]?.Call || 0) +
            (bulkInput?.payload?.contactType === 1 ? 1 : 0),
          Email:
            (leadBulkCommunications?.[input]?.Email || 0) +
            (bulkInput?.payload?.contactType === 2 ? 1 : 0),
          SMS:
            (leadBulkCommunications?.[input]?.SMS || 0) +
            (bulkInput?.payload?.contactType === 3 ? 1 : 0),
        };
      });
      return {
        ...state,
        leadsCommunication: leadBulkCommunications,
      };
    case LeadActionTypes.COMMUNICATION_COUNT_SUCCESS:
      const input = action as CommunicationCountSuccess;

      const leadCommunications: any = state?.leadsCommunication?.[input.id];
      return {
        ...state,
        leadsCommunication: {
          ...state?.leadsCommunication,
          [input?.id]: {
            WhatsApp:
              (leadCommunications?.WhatsApp || 0) +
              (input?.payload?.contactType === 0 ? 1 : 0),
            Call:
              (leadCommunications?.Call || 0) +
              (input?.payload?.contactType === 1 ? 1 : 0),
            Email:
              (leadCommunications?.Email || 0) +
              (input?.payload?.contactType === 2 ? 1 : 0),
            SMS:
              (leadCommunications?.SMS || 0) +
              (input?.payload?.contactType === 3 ? 1 : 0),
          },
        },
      };
    case LeadActionTypes.NAVIGATE_TO_LINK_SUCCESS:
      const inp = action as NavigateToLinkSuccess;

      const updatedLeads = state.leads.map((lead: any) => {
        if (lead.id === inp.resp?.leadId) {
          return {
            ...lead,
            links: lead.links.map((link: any) => {
              if (link.id === inp.resp?.linkId) {
                return {
                  ...link,
                  clickedCount: link.clickedCount + 1,
                };
              }
              return link;
            }),
          };
        }
        return lead;
      });

      return {
        ...state,
        leads: updatedLeads,
      };
    case LeadActionTypes.MEETING_OR_VISIT_DONE:
      return {
        ...state,
        isMeetingOrVisitDoneLoading: true,
      };
    case LeadActionTypes.MEETING_OR_VISIT_DONE_SUCCESS:
      return {
        ...state,
        isMeetingOrVisitDoneLoading: false,
      };
    case LeadActionTypes.ADD_LEAD_PROJECTS:
      return {
        ...state,
        isProjectAddLoading: true,
      };
    case LeadActionTypes.ADD_LEAD_PROJECTS_SUCCESS:
      return {
        ...state,
        isProjectAddLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_NOTES_LIST:
      return {
        ...state,
        leadNotesIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_NOTES_LIST_SUCCESS:
      return {
        ...state,
        leadNotes: (action as FetchLeadNotesListSuccess).data || [],
        leadNotesIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_CURRENCY_LIST:
      return {
        ...state,
        currencyListLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_CURRENCY_LIST_SUCCESS:
      return {
        ...state,
        leadCurrency: (action as FetchLeadCurrencySuccess).response,
        currencyListLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_ROTATION:
      return {
        ...state,
        leadRotationIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_ROTATION_SUCCESS:
      return {
        ...state,
        leadRotation: (action as FetchLeadRotationSuccess)?.resp?.items,
        leadRotationIsLoading: false,
      };
    case LeadActionTypes.UPDATE_LEAD_CUSTOM_STATUS_ENABLED:
      return {
        ...state,
        isCustomStatusEnabled: (action as UpdateLeadCustomStatusEnabled)
          ?.isCustomStatusEnabled,
      };
    case LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS:
      return {
        ...state,
        isCustomTopFiltersLoading: true,
        isCustomTopFiltersChildrenLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_SUCCESS:
      return {
        ...state,
        customTopFilters:
          (action as FetchLeadCustomTopFiltersSuccess)?.filteres || [],
        isCustomTopFiltersLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN:
      return {
        ...state,
        isCustomTopFiltersChildrenLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN_SUCCESS:
      return {
        ...state,
        customTopFiltersChildren:
          (action as FetchLeadCustomTopFiltersChildrenSuccess)?.filteres || [],
        isCustomTopFiltersChildrenLoading: false,
      };
    case LeadActionTypes.ADD_LEAD_CANCELLED:
      return {
        ...state,
        isAddLeadCancelled: (action as cancelAddLead).state,
      };
    case LeadActionTypes.ADD_DUPLICATE:
      return {
        ...state,
        isDuplicate: (action as addDuplicate).state,
        duplicateId: (action as addDuplicate).duplicateId,
      };
    case LeadActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        isMigrateExcelUploadedListLoading: true,
      };
    case LeadActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        excelMigrateUploadedList: (action as FetchMigrateExcelUploadedSuccess)
          .response,
        isMigrateExcelUploadedListLoading: false,
      };
    case LeadActionTypes.FETCH_BULK_OPERATION:
      return {
        ...state,
        isBulkOperationLoading: true,
      };
    case LeadActionTypes.FETCH_BULK_OPERATION_SUCCESS:
      return {
        ...state,
        bulkOperation: (action as FetchBulkOperationSuccess).response,
        isBulkOperationLoading: false,
      };
    case LeadActionTypes.FETCH_UPLOADTYPENAME_LIST:
      return {
        ...state,
        uploadTypeListIsLoading: true,
      };
    case LeadActionTypes.FETCH_UPLOADTYPENAME_LIST_SUCCESS:
      return {
        ...state,
        uploadtypeNameList: (action as FetchUploadTypeNameListSuccess).response,
        uploadTypeListIsLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_HISTORY:
      return {
        ...state,
        leadHistoryIsLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_HISTORY_SUCCESS:
      return {
        ...state,
        leadHistory: (action as FetchLeadHistoryListSuccess).data || [],
        leadHistoryIsLoading: false,
      };
    case LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_LIST:
      return {
        ...state,
        additionalPropertyIsLoading: true,
      };
    case LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_LIST_SUCCESS:
      return {
        ...state,
        additionalProperty: (action as FetchAdditionalPropertyListSuccess).response,
        additionalPropertyIsLoading: false,
      };
    case LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_VALUE:
      return {
        ...state,
        additionalPropertyValueIsLoading: true,
      };
    case LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_VALUE_SUCCESS:
      return {
        ...state,
        additionalPropertyValue: (action as FetchAdditionalPropertyValueSuccess).response,
        additionalPropertyValueIsLoading: false,
      };
    case LeadActionTypes.FETCH_CAMPAIGN_LIST:
      return {
        ...state,
        campaignIsLoading: true,
      };
    case LeadActionTypes.FETCH_CAMPAIGN_LIST_SUCCESS:
      return {
        ...state,
        campaignList: (action as FetchCampaignListSuccess).response,
        campaignIsLoading: false,
      };
    case LeadActionTypes.FETCH_COUNTRY_BASED_CITY_SUCCESS:
      return {
        ...state,
        countryBasedCity: (action as FetchCountryBasedCitySuccess).resp,
        countryBasedCityLoading: false,
      }
    case LeadActionTypes.FETCH_COUNTRY_BASED_CITY:
      return {
        ...state,
        countryBasedCityLoading: true,
      }
    case LeadActionTypes.FETCH_CAMPAIGN_LIST_ANONYMOUS:
      return {
        ...state,
        campaignListAnonymousIsLoading: true,
      };
    case LeadActionTypes.FETCH_CAMPAIGN_LIST_ANONYMOUS_SUCCESS:
      return {
        ...state,
        campaignListAnonymous: (action as FetchCampaignListAnonymousSuccess).response,
        campaignListAnonymousIsLoading: false,
      };
    case LeadActionTypes.IS_CARDVIEW:
      return {
        ...state,
        isCardView: (action as UpdateIsCardView)?.isCardView
      }
    case LeadActionTypes.FETCH_LEAD_NATIONALITY:
      return {
        ...state,
        leadNationalityLoading: true,
      }
    case LeadActionTypes.FETCH_LEAD_NATIONALITY_SUCCESS:
      return {
        ...state,
        leadNationality: (action as FetchLeadNationalitySuccess).resp,
        leadNationalityLoading: false,
      }
    case LeadActionTypes.FETCH_LEAD_CLUSTER_NAME:
      return {
        ...state,
        leadClusterNameLoading: true,
      }
    case LeadActionTypes.FETCH_LEAD_CLUSTER_NAME_SUCCESS:
      return {
        ...state,
        leadClusterName: (action as FetchLeadClusterNameSuccess).resp,
        leadClusterNameLoading: false,
      }
    case LeadActionTypes.FETCH_LEAD_UNIT_NAME:
      return {
        ...state,
        leadUnitNameLoading: true,
      }
    case LeadActionTypes.FETCH_LEAD_UNIT_NAME_SUCCESS:
      return {
        ...state,
        leadUnitName: (action as FetchLeadUnitNameSuccess).resp,
        leadUnitNameLoading: false,
      }
    case LeadActionTypes.FETCH_ALL_PARENT_LEAD_BY_ID:
      return {
        ...state,
        isParentLeadDataLoading: true,
      };
    case LeadActionTypes.FETCH_ALL_PARENT_LEAD_BY_ID_SUCCESS:
      return {
        ...state,
        allParentLeadData: (action as FetchAllParentLeadByIdSuccess).response,
        isParentLeadDataLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_BY_ID_WITH_ARCHIVE:
      return {
        ...state,
        isLeadByIdWithArchiveLoading: true,
      };
    case LeadActionTypes.FETCH_LEAD_BY_ID_WITH_ARCHIVE_SUCCESS:
      return {
        ...state,
        leadByIdWithArchive: (action as FetchAllParentLeadByIdSuccess).response,
        isLeadByIdWithArchiveLoading: false,
      };
    case LeadActionTypes.FETCH_LEAD_POSTAL_CODE:
      return {
        ...state,
        leadPostalCodeLoading: true,
      }
    case LeadActionTypes.FETCH_LEAD_POSTAL_CODE_SUCCESS:
      return {
        ...state,
        leadPostalCode: (action as FetchLeadPostalCodeSuccess).resp,
        leadPostalCodeLoading: false,
      }
    case LeadActionTypes.FETCH_LEAD_LANDLINE:
      return {
        ...state,
        leadLandLineLoading: true,
      }
    case LeadActionTypes.FETCH_LEAD_LANDLINE_SUCCESS:
      return {
        ...state,
        leadLandLine: (action as FetchLeadLandLineSuccess).resp,
        leadLandLineLoading: false,
      }
    default:
      return state;
  }
}
// TODO: uncomment accordingly when data-binding
export const selectFeature = (state: AppState) => state.lead;

export const getLeads = createSelector(selectFeature, (state: LeadState) => {
  return state.leads;
});

export const getLeadsCount = createSelector(
  selectFeature,
  (state: LeadState) => {
    return {
      totalCount: state.totalCount,
      itemsCount: state.itemsCount,
      count: state.count,
    };
  }
);

export const getColumnHeadings = createSelector(
  selectFeature,
  (state: LeadState) => {
    return state.excelColumnHeading;
  }
);

export const getDuplicateLeads = createSelector(
  selectFeature,
  (state: LeadState) => {
    return state.duplicateLeads;
  }
);

export const getFiltersPayload = createSelector(
  selectFeature,
  (state: LeadState) => {
    return state.filtersPayload;
  }
);

export const getInvoiceFiltersPayload = createSelector(
  selectFeature,
  (state: LeadState) => {
    return state.invoiceFiltersPayload;
  }
);

export const getLeadsDocuments = createSelector(
  selectFeature,
  (state: LeadState) => state.uploadDoc
);

export const getProjectList = createSelector(
  selectFeature,
  (state: LeadState) => state.projectList
);

export const getProjectListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.projectListIsLoading
);

export const getQRProjectList = createSelector(
  selectFeature,
  (state: LeadState) => state.qrProjectList
);

export const getPropertyList = createSelector(
  selectFeature,
  (state: LeadState) => state.propertyList
);

export const getNotesList = createSelector(
  selectFeature,
  (state: LeadState) => {
    return {
      LeadNotes: state.leadNotes,
      LeadNoteIsLoading: state.leadNotesIsLoading,
    };
  }
);

export const getPropertyListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.propertyListIsLoading
);

export const getQRPropertyList = createSelector(
  selectFeature,
  (state: LeadState) => state.qrPropertyList
);

export const getExcelUploadedList = createSelector(
  selectFeature,
  (state: LeadState) => state.excelUploadedList
);

export const getExportStatus = createSelector(
  selectFeature,
  (state: LeadState) => state.exportStatus
);

export const getLocations = createSelector(
  selectFeature,
  (state: LeadState) => state.locations
);

export const getLocationsIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.locationsIsLoading
);

export const getLeadCities = createSelector(
  selectFeature,
  (state: LeadState) => state.cities
);

export const getLeadCitiesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.citiesIsLoading
);

export const getLeadStates = createSelector(
  selectFeature,
  (state: LeadState) => state.states
);

export const getLeadStatesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.statesIsLoading
);

export const getLeadCountries = createSelector(
  selectFeature,
  (state: LeadState) => state.countries
);

export const getLeadCountriesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.countriesIsLoading
);

export const getLeadSubCommunities = createSelector(
  selectFeature,
  (state: LeadState) => state.subCommunities
);

export const getLeadSubCommunitiesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.subCommunitiesIsLoading
);

export const getLeadCommunities = createSelector(
  selectFeature,
  (state: LeadState) => state.communities
);

export const getLeadCommunitiesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.communitiesIsLoading
);

export const getLeadTowerNames = createSelector(
  selectFeature,
  (state: LeadState) => state.towerNames
);

export const getLeadTowerNamesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.towerNamesIsLoading
);

export const getLeadLocalites = createSelector(
  selectFeature,
  (state: LeadState) => state.localites
);

export const getLeadLocalitesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.localitesIsLoading
);

export const getLeadZones = createSelector(
  selectFeature,
  (state: LeadState) => state.zones
);

export const getLeadZonesIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.zonesIsLoading
);

export const getMatchingPropertiesOrProjects = createSelector(
  selectFeature,
  (state: LeadState) => state.matchingPropertiesOrProjects
);
export const getIsMatchingLeadLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isMatchingPropertyOrProjectLoading
);
export const getSubSourceList = createSelector(
  selectFeature,
  (state: LeadState) => state.subSourceList
);

export const getSubSourceListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.subSourceListIsLoading
);

export const getLeadsAgencyNameList = createSelector(
  selectFeature,
  (state: LeadState) => state.agencyNameList
);

export const getLeadExport = createSelector(
  selectFeature,
  (state: LeadState) => state.leadExport
);

export const getDuplicateFeature = createSelector(
  selectFeature,
  (state: LeadState) => state.duplicateFeature
);

export const getLeadBaseFilterCount = createSelector(
  selectFeature,
  (state: LeadState) => state.baseFilterCount
);

export const getAlreadyAssignedInfo = createSelector(
  selectFeature,
  (state: LeadState) => state.alreadyAssignData
);

export const getBaseFilterCountIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.baseFilterCount.isLoading
);

export const getActiveLeadsCountsIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.activeLeadsCount.isLoading
);

export const getActiveLeadsCounts = createSelector(
  selectFeature,
  (state: LeadState) => state.activeLeadsCount
);

export const getNotInterestedCounts = createSelector(
  selectFeature,
  (state: LeadState) => state.notInterestedLeadsCount
);

export const getLeadStatusCounts = createSelector(
  selectFeature,
  (state: LeadState) => state.statusCount
);

export const getDroppedCounts = createSelector(
  selectFeature,
  (state: LeadState) => state.droppedLeadsCount
);

export const getFlagsCounts = createSelector(
  selectFeature,
  (state: LeadState) => state.flagsCount
);

export const getBulkReassignDetails = createSelector(
  selectFeature,
  (state: LeadState) => state.bulkReassignDetails
);

export const getBulk2ndReassignDetails = createSelector(
  selectFeature,
  (state: LeadState) => state.bulk2ndReassignDetails
);

export const getLeadListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isLoading
);

export const getLeadListIsPosting = createSelector(
  selectFeature,
  (state: LeadState) => state.isLeadPosting
);

export const getLeadStatusIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadStatusIsLoading
);

export const getMultipleLeadStatusIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.multipleLeadStatusIsLoading
);

export const getReassignLeadIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.reassignLeadIsLoading
);

export const getReassignBothIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.reassignBothIsLoading
);

export const getBulkReassignLeadIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.bulkReassignLeadIsLoading
);

export const getBulk2ndReassignIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.bulk2ndReassignIsLoading
);

export const getBulkSourceIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.bulkSourceIsLoading
);

export const getBulkProjectIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.bulkProjectsIsLoading
);

export const getBulkDeleteLeadsIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.bulkDeleteLeadsIsLoading
);

export const getBulkRestoreLeadsIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.bulkRestoreLeadsIsLoading
);

export const getActiveLeadIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.activeLeadIsLoading
);

export const getActiveLeadAppointmentsIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.activeLeadAppointmentsIsLoading
);

export const getActiveLeadAppointments = createSelector(
  selectFeature,
  (state: LeadState) => state.activeLeadAppointments
);

export const getLeadsCommunication = createSelector(
  selectFeature,
  (state: LeadState) => state.leadsCommunication
);

export const getLeadsCommunicationIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadsCommunicationIsLoading
);

export const getIsMeetingOrVisitDoneLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isMeetingOrVisitDoneLoading
);

export const getIsProjectAddIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isProjectAddLoading
);

export const getActiveLead = createSelector(
  selectFeature,
  (state: LeadState) => state.activeLead
);

export const getLeadCurrencyList = createSelector(
  selectFeature,
  (state: LeadState) => state.leadCurrency
);

export const getLeadCurrencyListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.currencyListLoading
);

export const getLeadRotationList = createSelector(
  selectFeature,
  (state: LeadState) => state.leadRotation
);

export const getLeadRotationListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadRotationIsLoading
);

export const getIsLeadCustomStatusEnabled = createSelector(
  selectFeature,
  (state: LeadState) => state.isCustomStatusEnabled
);

export const getLeadCustomTopFilters = createSelector(
  selectFeature,
  (state: LeadState) => state.customTopFilters
);

export const getLeadCustomTopFiltersChildren = createSelector(
  selectFeature,
  (state: LeadState) => state.customTopFiltersChildren
);

export const getLeadCustomTopFiltersIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isCustomTopFiltersLoading
);

export const getLeadCustomTopFiltersChildrenIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isCustomTopFiltersChildrenLoading
);
export const getIsAddLeadCancelled = createSelector(
  selectFeature,
  (state: LeadState) => state.isAddLeadCancelled
);

export const getExportStatusIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isExportStatusLoading
);

export const getExcelUploadedListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isExcelUploadedListLoading
);

export const getDuplicate = createSelector(selectFeature, (state) => ({
  isDuplicate: state.isDuplicate,
  duplicateId: state.duplicateId,
}));

export const getMigrateExcelUploadedList = createSelector(
  selectFeature,
  (state: LeadState) => state.excelMigrateUploadedList
);

export const getExcelMigrateUploadedListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isMigrateExcelUploadedListLoading
);

export const getBulkOperation = createSelector(
  selectFeature,
  (state: LeadState) => state.bulkOperation
);

export const getBulkOperationIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isBulkOperationLoading
);

export const getUploadTypeNameListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.uploadTypeListIsLoading
);

export const getUploadTypeNameList = createSelector(
  selectFeature,
  (state: LeadState) => state.uploadtypeNameList
);

export const getAgencyNameList = createSelector(
  selectFeature,
  (state: LeadState) => state.agencyNameList
);

export const getAgencyNameListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.agencyNameListIsLoading
);

export const getLeadHistory = createSelector(
  selectFeature,
  (state: LeadState) => state.leadHistory
);

export const getLeadHistoryIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadHistoryIsLoading
);

export const getAdditionalProperty = createSelector(
  selectFeature,
  (state: LeadState) => state.additionalProperty
);

export const getAdditionalPropertyIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.additionalPropertyIsLoading
);

export const getAdditionalPropertyValue = createSelector(
  selectFeature,
  (state: LeadState) => state.additionalPropertyValue
);

export const getAdditionalPropertyValueIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.additionalPropertyValueIsLoading
);

export const getCampaignListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.campaignIsLoading
);

export const getCampaignList = createSelector(
  selectFeature,
  (state: LeadState) => state.campaignList
);

export const getCampaignListAnonymous = createSelector(
  selectFeature,
  (state: LeadState) => state.campaignListAnonymous
);

export const getCampaignListAnonymousIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.campaignListAnonymousIsLoading
);

export const getAgencyNameListAnonymous = createSelector(
  selectFeature,
  (state: LeadState) => state.agencyNameListAnonymous
);

export const getAgencyNameListAnonymousIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.agencyNameListAnonymousIsLoading
);

export const getChannelPartnerList = createSelector(
  selectFeature,
  (state: LeadState) => state.channelPartnerList
);

export const getChannelPartnerListIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.channelPartnerListIsLoading
);

export const getChannelPartnerListAnonymous = createSelector(
  selectFeature,
  (state: LeadState) => state.channelPartnerListAnonymous
);

export const getChannelPartnerListAnonymousIsLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.channelPartnerListAnonymousIsLoading
);

export const getCountryBasedCity = createSelector(
  selectFeature,
  (state: LeadState) => state.countryBasedCity
)

export const getCountryBasedCityLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.countryBasedCityLoading
)

export const getLeadCardData = createSelector(
  selectFeature,
  (state: LeadState) => state.cardData
)
export const getIsLoadMore = createSelector(
  selectFeature,
  (state: LeadState) => state.isLoadMore

)

export const getLeadNationality = createSelector(
  selectFeature,
  (state: LeadState) => state.leadNationality
)

export const getLeadNationalityLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadNationalityLoading
)

export const getLeadClusterName = createSelector(
  selectFeature,
  (state: LeadState) => state.leadClusterName
)

export const getLeadClusterNameLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadClusterNameLoading
)

export const getLeadUnitName = createSelector(
  selectFeature,
  (state: LeadState) => state.leadUnitName
)

export const getLeadUnitNameLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadUnitNameLoading
)

export const getAllParentLeadData = createSelector(
  selectFeature,
  (state: LeadState) => state.allParentLeadData
)

export const getParentLeadDataLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isParentLeadDataLoading
)

export const getLeadByIdWithArchiveData = createSelector(
  selectFeature,
  (state: LeadState) => state.leadByIdWithArchive
)

export const getIsLeadByIdWithArchiveLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.isLeadByIdWithArchiveLoading
)

export const getLeadPostalCode = createSelector(
  selectFeature,
  (state: LeadState) => state.leadPostalCode
)

export const getLeadPostalCodeLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadPostalCodeLoading
)

export const getLeadLandLine = createSelector(
  selectFeature,
  (state: LeadState) => state.leadLandLine
)

export const getLeadLandLineLoading = createSelector(
  selectFeature,
  (state: LeadState) => state.leadLandLineLoading
)
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AssignmentCategoryType, UserAssignmentType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, changeCalendar, getAssignedToDetails, patchTime, validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchUserAssignmentByEntity, UpdateMultiUserAssignment, UpdateUserAssignment } from 'src/app/reducers/automation/automation.actions';
import { getPriorityList, getUserAssignmentByEntity } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import { getProjectsIDWithName, getProjectsIDWithNameIsLoading } from 'src/app/reducers/project/project.reducer';
import { FetchPropertyAssignments, FetchPropertyWithIdNameList } from 'src/app/reducers/property/property.actions';
import { getPropertyAssignments, getPropertyWithIdLoading, getPropertyWithIdNameList } from 'src/app/reducers/property/property.reducer';
import { FetchAllTeams } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getAllTeamsList, getAllTeamsListIsLoading, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'integration-assignment-v2',
  templateUrl: './integration-assignment-v2.component.html',
})
export class IntegrationAssignmentV2Component implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  @Input() assignedUserDetails: Array<string>;
  @Output() isShowAssignModalChanged: EventEmitter<boolean> = new EventEmitter<boolean>();
  isAdAccount: boolean;
  gridApi: any;
  fbAccountName: string;
  isFormAccount: boolean;
  isBulkFb: boolean
  selectedAdName: string;
  isFbComponent: boolean;
  isBulkAssignModel: boolean;
  selectedIntegrations: any[] = [];
  integration: any;
  canAllowSecondaryUsers: boolean = false;
  canAllowDuplicates: boolean = false;
  canAssignToAny: boolean = false;
  sameAsPrimaryUsers: boolean = false;
  sameAsSelectedUsers: boolean = false;
  sameAsAbove: boolean = false;
  assignedSecondaryUsers: any[] = [];
  assignedDuplicateUser: any[] = [];
  assignedPrimaryUsers: any[] = [];
  assignedUser: any[] = [];
  allActiveUsers: any[] = [];
  allUserList: any[] = [];
  userList: any[] = [];
  activeUsers: any[] = [];
  updatedIntegrationList: any[] = [];
  moduleId: string;
  selectedAccountId: string;
  canEnableAllowDuplicates: boolean = false;
  canEnableAllowSecondaryUsers: boolean = false;
  canAssignSequentially: boolean = false;

  leadRotationAddGroupForm: FormGroup;
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  configForm: FormGroup;
  allowDuplicatesPopupRef: any;
  selectedOption: 'users' | 'teams' | 'projects' | 'properties' = 'users';
  leadRotationEnabled: boolean = false;
  IsAssignedCallLogsEnabled: boolean = false;
  teamsList: any[] = [];
  projectsList: any[] = [];
  teamLeaders: any[] = [];
  selectedTeam: any = null;
  currentDate = new Date();
  rotationNumOptions: { label: string; value: number }[] = [
    { label: '1', value: 1 },
    { label: '2', value: 2 },
    { label: '3', value: 3 },
    { label: '4', value: 4 },
    { label: '5', value: 5 }
  ];
  allTeamsIsLoading: boolean = false;
  projectsListIsLoading: boolean = false;
  propertiesListIsLoading: boolean = false;
  propertiesList: any[] = [];
  selectedProject: any;
  userData: any;
  entityDetails: any;

  // Track which users are checked in the selected section
  checkedUsers: { [key: string]: boolean } = {};
  selectedProperty: any;

  getDropdownLabel(): string {
    if (this.selectedOption === 'users') return 'Select Users';
    if (this.selectedOption === 'teams') return 'Select Team';
    if (this.selectedOption === 'projects') return 'Select Project';
    if (this.selectedOption === 'properties') return 'Select Property';
    return 'Select an Option';
  }

  selectedSectionLeadAssignment: "Configuration" | "AdditionalConfig" = "Configuration";
  leadAssignmentOptions: { name: string; value: string }[] = [
    {
      name: 'Assignment Config',
      value: 'Configuration'
    },
    {
      name: "Additional Config",
      value: 'AdditionalConfig'
    }
  ];
  listSelection: string = 'original';
  selectedUserType: 'Primary User(s)' | 'Secondary User(s)' = 'Primary User(s)';
  message: string;
  notes: string;
  addditionalAssigments: ['Real Time Duplicates', 'Secondary Users']
  getAssignedToDetails = getAssignedToDetails;
  userPercentages: { [key: string]: number } = {};

  get filteredUsers(): string[] {
    return this.listSelection === 'original' ? this.assignedUser : this.assignedDuplicateUser;
  }

  get primarySeondaryUsers(): string[] {
    return this.listSelection === 'primary' ? this.assignedPrimaryUsers :
      this.listSelection === 'secondary' ? this.assignedSecondaryUsers : this.assignedDuplicateUser;
  }
  constructor(
    private store: Store<AppState>,
    private _notificationService: NotificationsService,
    private fb: FormBuilder,
    public modalService: BsModalService,
    private modalRef: BsModalRef,
    public router: Router,
  ) { }

  ngOnInit(): void {
    console.log(this.fbAccountName);

    this.initializeForms();
    if (this.isBulkFb) {
      this.selectedAccountId = this.integration.map((item: any) => item.data?.id);
    }
    if (!this.selectedAccountId)
      this.selectedAccountId = this.integration.accountId;
    this.store.dispatch(new FetchAllTeams());
    this.filterTeamLeader();
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
        }
      });
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });

    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.selectAllForDropdownItems(this.activeUsers);
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
        this.selectAllForDropdownItems(this.allActiveUsers);
      });

    this.store
      .select(getAllTeamsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.teamsList = data;
        if (this.selectedTeam) {
          const foundTeam = this.teamsList.find(team => team.id === this.selectedTeam.id);
          if (foundTeam) {
            this.selectedTeam = foundTeam;
            // this.updateLeadRotationForm(); 
            this.store
              .select(getUserAssignmentByEntity)
              .pipe(takeUntil(this.stopper))
              .subscribe((data: any) => {
                this.assignedUser = data?.userIds;
                this.filterTeamLeader();
              });
          }
        }
      });

    this.store
      .select(getAllTeamsListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.allTeamsIsLoading = loading;
      });

    this.store.dispatch(new FetchProjectIdWithName());
    this.store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectsList = data?.filter((item: any) => item)
          ?.slice()
          ?.sort((a: any, b: any) => a.name?.localeCompare(b.name));
      });

    this.store
      .select(getProjectsIDWithNameIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.projectsListIsLoading = loading;
      });
    this.store.dispatch(new FetchPropertyWithIdNameList());
    this.store
      .select(getPropertyWithIdNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertiesList = data?.filter((item: any) => item)
          ?.slice()
          ?.sort((a: any, b: any) => a.name?.localeCompare(b.name));
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });

    this.store
      .select(getPropertyWithIdLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.propertiesListIsLoading = loading;
      });
    // this.configForm.get('assignmentType')?.valueChanges.subscribe(() => {
    //   this.toggleAssignedUserValidation();
    // });
    this.initializeUserPercentages();
    if (!this.isBulkAssignModel && !this.isBulkFb) {
      if (this.selectedAccountId) {
        this.store.dispatch(new FetchUserAssignmentByEntity(this.selectedAccountId));
      }

      this.store
        .select(getUserAssignmentByEntity)
        .pipe(takeUntil(this.stopper))
        .subscribe((res: any) => {
          this.entityDetails = res;
          this.canAssignSequentially = !res?.shouldCreateMultipleDuplicates;
          this.canAllowSecondaryUsers = res?.isDualAssignmentEnabled;
          this.canAllowDuplicates = res?.isDuplicateAssignmentEnabled;
          this.assignedUser = res?.userIds || [];
          this.assignedUserDetails = this.assignedUser;
          this.assignedPrimaryUsers = this.assignedUser;
          this.assignedSecondaryUsers = res?.secondaryUserIds || [];
          this.assignedDuplicateUser = res?.duplicateUserIds || [];

          let startTime = patchTime(
            this.entityDetails?.leadsAssignRotationInfo?.leadRotationConfiguration?.startTime,
            this.userData?.timeZoneInfo?.baseUTcOffset
          );
          let endTime = patchTime(
            this.entityDetails?.leadsAssignRotationInfo?.leadRotationConfiguration?.endTime,
            this.userData?.timeZoneInfo?.baseUTcOffset
          );

          // this.leadRotationAddGroupForm.patchValue({
          //   teamName: this.entityDetails?.leadsAssignRotationInfo?.groupName || null,
          //   teamLeader: this.entityDetails?.leadsAssignRotationInfo?.groupManager || null,
          //   shiftTimeFrom: startTime || null,
          //   shiftTimeTo: endTime || null,
          //   rotationTime: this.timeToMinutes(this.entityDetails?.leadsAssignRotationInfo?.leadRotationConfiguration?.timeInterval) || null,
          //   rotationNum: this.entityDetails?.leadsAssignRotationInfo?.noOfRotation || null,
          // });

          this.integrationDuplicateForm.patchValue({
            assignedUser: this.assignedUser,
            selectedTeam: this.entityDetails?.teamName,
            ...(this.entityDetails?.project ? { selectedProject: this.entityDetails?.project?.name } : {}),
            ...(this.entityDetails?.property ? { selectedProperty: this.entityDetails?.property?.title } : {}),
          });

          // Default categoryType to SequentialBased
          const categoryType = res?.categoryType ?? AssignmentCategoryType.SequentialBased;
          const isPercentageBased = categoryType === AssignmentCategoryType.PercentageBased;
          this.configForm.get('categoryType').setValue(isPercentageBased);
          this.IsAssignedCallLogsEnabled = isPercentageBased;

          // Default userAssignmentType to 'users'
          const assignmentTypeMap: Record<number, 'users' | 'teams' | 'projects' | 'properties'> = {
            [UserAssignmentType.User]: 'users',
            [UserAssignmentType.Team]: 'teams',
            [UserAssignmentType.Project]: 'projects',
            [UserAssignmentType.Property]: 'properties'
          };
          const resolvedAssignmentType = assignmentTypeMap[res?.userAssignmentType ?? UserAssignmentType.User];
          this.configForm.get('assignmentType').setValue(resolvedAssignmentType);
          this.selectedOption = resolvedAssignmentType;

          if (res?.shouldConfigureLeadRotation !== undefined) {
            this.configForm.get('leadRotationEnabled').setValue(res.shouldConfigureLeadRotation);
            this.leadRotationEnabled = res.shouldConfigureLeadRotation;
          }

          if (res?.userAssignmentConfigurations?.length > 0) {
            this.userPercentages = {};
            res.userAssignmentConfigurations.forEach((config: { userId: string; percentage: number }) => {
              if (config.userId && config.percentage !== undefined) {
                this.userPercentages[config.userId] = config.percentage;
              }
            });
          }

          // this.toggleAssignedUserValidation();
          this.initializeCheckedUsers();
          this.filterTeamLeader();
        });
    }


  }

  initializeForms() {
    this.integrationDuplicateForm = this.fb.group({
      assignedUser: [null],
      assignedDuplicateUser: [null, [Validators.required]],
      selectedProject: [null],
      selectedTeam: [null],
      selectedProperty: [null],
    });

    this.integrationDuplicateForm.get('assignedUser').valueChanges.subscribe((users: any[]) => {
      if (users && users.length > 0) {
        this.initializeCheckedUsers();
        this.filterTeamLeader();
      }
    });

    this.integrationDualOwnerForm = this.fb.group({
      assignedSecondaryUsers: [null, Validators.required],
      assignedDuplicateUser: [null, Validators.required],
      selectedUserType: ['Primary User(s)', Validators.required]
    });

    this.leadRotationAddGroupForm = this.fb.group({
      teamName: [null, Validators.required],
      teamLeader: [null, Validators.required],
      shiftTimeFrom: [null, Validators.required],
      shiftTimeTo: [null, [Validators.required]],
      rotationTime: [null, Validators.required],
      rotationNum: [null, Validators.required]
    });

    this.leadRotationAddGroupForm.get('teamLeader').valueChanges.subscribe((leaderId: string) => {
      if (leaderId) {
        if (this.assignedUser && this.assignedUser.includes(leaderId)) {
          this.assignedUser = this.assignedUser.filter(id => id !== leaderId);
          delete this.checkedUsers[leaderId];
          this.initializeCheckedUsers();
        }
      }
    });

    this.configForm = this.fb.group({
      categoryType: [false],
      assignmentType: ['users'],
      leadRotationEnabled: [false]
    });

    this.configForm.get('categoryType').valueChanges.subscribe((value: boolean) => {
      this.IsAssignedCallLogsEnabled = value;
      if (value) {
        this.initializeUserPercentages();
      }
    });

    this.configForm.get('assignmentType').valueChanges.subscribe((value: 'users' | 'teams' | 'projects' | 'properties') => {
      if (this.selectedOption !== value) {
        this.selectedOption = value;
        const assignmentTypeMap: Record<string, UserAssignmentType> = {
          'users': UserAssignmentType.User,
          'teams': UserAssignmentType.Team,
          'projects': UserAssignmentType.Project,
          'properties': UserAssignmentType.Property
        };

        if (this.entityDetails?.userAssignmentType !== assignmentTypeMap[value]) {
          this.assignedUser = [];
          if (value === 'teams') {
            this.integrationDuplicateForm.get('selectedTeam').setValue(null);
          } else if (value === 'projects') {
            this.integrationDuplicateForm.get('selectedProject').setValue(null);
          } else if (value === 'properties') {
            this.integrationDuplicateForm.get('selectedProperty').setValue(null);
          }
          if (this.IsAssignedCallLogsEnabled) {
            this.userPercentages = {};
          }
        } else {
          this.assignedUser = this.entityDetails?.userIds;
          this.initializeCheckedUsers();
        }
      }
    });

    this.configForm.get('leadRotationEnabled').valueChanges.subscribe((value: boolean) => {
      this.leadRotationEnabled = value;
    });
  }

  // toggleAssignedUserValidation(): void {
  //   const assignmentType = this.configForm.get('assignmentType')?.value;
  //   toggleValidation(VALIDATION_CLEAR, this.integrationDuplicateForm, 'assignedUser');
  //   toggleValidation(VALIDATION_CLEAR, this.integrationDuplicateForm, 'selectedTeam');
  //   toggleValidation(VALIDATION_CLEAR, this.integrationDuplicateForm, 'selectedProject');
  //   toggleValidation(VALIDATION_CLEAR, this.integrationDuplicateForm, 'selectedProperty');
  //   switch (assignmentType) {
  //     case 'users':
  //       toggleValidation(VALIDATION_SET, this.integrationDuplicateForm, 'assignedUser', [Validators.required]);
  //       break;
  //     case 'teams':
  //       toggleValidation(VALIDATION_SET, this.integrationDuplicateForm, 'selectedTeam', [Validators.required]);
  //       break;
  //     case 'projects':
  //       toggleValidation(VALIDATION_SET, this.integrationDuplicateForm, 'selectedProject', [Validators.required]);
  //       break;
  //     case 'properties':
  //       toggleValidation(VALIDATION_SET, this.integrationDuplicateForm, 'selectedProperty', [Validators.required]);
  //       break;
  //   }
  // }

  removeUserFromSelection(userId: any) {
    if (this.canAllowSecondaryUsers) {
      this.sameAsPrimaryUsers = false;
      if (this.listSelection == 'secondary') {
        this.assignedSecondaryUsers = this.assignedSecondaryUsers?.filter((user: any) => user !== userId);
        if (!this.assignedSecondaryUsers?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      } else if (this.listSelection == 'duplicate') {
        this.assignedDuplicateUser = this.assignedDuplicateUser?.filter((user: any) => user !== userId);
        if (!this.assignedDuplicateUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';
        return;
      }
      this.assignedPrimaryUsers = this.assignedPrimaryUsers?.filter((user: any) => user !== userId);
      if (!this.assignedPrimaryUsers?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
      this.sameAsSelectedUsers = false;
      if (this.listSelection == 'original') {
        this.assignedUser = this.assignedUser.filter((user: any) => user !== userId);
        this.assignedUserDetails = this.assignedUser;
        if (!this.assignedUser?.length)
          this.selectedSectionLeadAssignment = 'Configuration';

        if (this.IsAssignedCallLogsEnabled) {
          this.initializeUserPercentages();
        }
        return;
      }
      this.assignedDuplicateUser = this.assignedDuplicateUser.filter((user: any) => user !== userId);
      if (!this.assignedDuplicateUser?.length)
        this.selectedSectionLeadAssignment = 'Configuration';
      return;
    }
    this.assignedUser = this.assignedUser.filter((user: any) => user !== userId);
    this.assignedUserDetails = this.assignedUser;
    if (!this.assignedUser?.length)
      this.selectedSectionLeadAssignment = 'Configuration';

    if (this.IsAssignedCallLogsEnabled) {
      this.initializeUserPercentages();
    }
    delete this.checkedUsers[userId];
    this.filterTeamLeader();
  }

  assignFbAccount() {
    this.syncPrimaryAndAssignedUsers();

    // if (this.configForm.get('leadRotationEnabled').value) {
    //   if (!this.leadRotationAddGroupForm?.valid) {
    //     validateAllFormFields(this.leadRotationAddGroupForm);
    //     return;
    //   }
    // }
    if (this.getTotalPercentage() !== 100 && this.configForm.get('categoryType').value) {
      this._notificationService.warn('Warning', "Total percentage should be 100%");
      return;
    }
    if (this.assignedUser?.length && (!this.assignedPrimaryUsers?.length || this.assignedPrimaryUsers.length === 0)) {
      this.assignedPrimaryUsers = [...this.assignedUser];
    }

    if (!this.validateAssignmentForm())
      return;

    // if (!this.assignedUser?.length) {
    //   validateAllFormFields(this.integrationDuplicateForm);
    //   return;
    // }

    const userAssignmentConfigurations = this.assignedUser.map(userId => ({
      userId,
      percentage: this.configForm.get('categoryType').value ? this.userPercentages[userId] : 0
    }));

    const assignmentTypeMap: Record<string, UserAssignmentType> = {
      'users': UserAssignmentType.User,
      'teams': UserAssignmentType.Team,
      'projects': UserAssignmentType.Project,
      'properties': UserAssignmentType.Property
    };
    let createLeadRotationInfo;
    // if (this.configForm.get('leadRotationEnabled').value) {
    //   createLeadRotationInfo = {
    //     id: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.id : null,
    //     groupName: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.groupName : this.leadRotationAddGroupForm.get('teamName').value,
    //     groupManager: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.groupManager : this.leadRotationAddGroupForm.get('teamLeader').value,
    //     noOfRotation: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.noOfRotation : this.leadRotationAddGroupForm.get('rotationNum').value || this.leadRotationAddGroupForm.get('rotationNum').value,
    //     configuration: {
    //       id: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.configuration?.id : null,
    //       timeInterval: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.configuration?.timeInterval : this.convertMinutesToFormat(Number(this.leadRotationAddGroupForm.get('rotationTime').value)),
    //       startTime: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.configuration?.startTime : setTimeZoneTime(this.leadRotationAddGroupForm.get('shiftTimeFrom').value, this.userData?.timeZoneInfo?.baseUTcOffset),
    //       endTime: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.configuration?.endTime : setTimeZoneTime(this.leadRotationAddGroupForm.get('shiftTimeTo').value, this.userData?.timeZoneInfo?.baseUTcOffset)
    //     },
    //     integrationAccountInfoId: this.selectedAccountId
    //   };
    // }
    let payload: any = {
      entityId: this.selectedAccountId,
      userIds: this.canAllowSecondaryUsers ? (this.assignedPrimaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedPrimaryUsers) :
        (this.assignedUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedUser) || [],
      secondaryUserIds: this.assignedSecondaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedSecondaryUsers || [],
      duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedDuplicateUser || [],
      isDuplicateAssignmentEnabled: this.canAllowDuplicates || false,
      isDualAssignmentEnabled: this.canAllowSecondaryUsers || false,
      moduleId: this.moduleId,
      shouldCreateMultipleDuplicates: !this.canAssignSequentially,
      userAssignmentConfigurations: userAssignmentConfigurations,
      // shouldConfigureLeadRotation: this.configForm.get('leadRotationEnabled').value,
      categoryType: this.configForm.get('categoryType').value ? AssignmentCategoryType.PercentageBased : AssignmentCategoryType.SequentialBased,
      userAssignmentType: assignmentTypeMap[this.configForm.get('assignmentType').value as string] || UserAssignmentType.User,
      // createLeadRotationInfo: createLeadRotationInfo,
      property: this.integrationDuplicateForm.value.selectedProperty?.id || this.entityDetails?.property?.id ? {
        id: this.integrationDuplicateForm.value.selectedProperty?.id || this.entityDetails?.property?.id,
        title: this.integrationDuplicateForm.value.selectedProperty?.title || this.entityDetails?.property?.title
      } : null,
      project: this.integrationDuplicateForm.value.selectedProject?.id || this.entityDetails?.project?.id ? {
        id: this.integrationDuplicateForm.value.selectedProject?.id || this.entityDetails?.project?.id,
        name: this.integrationDuplicateForm.value.selectedProject?.name || this.entityDetails?.project?.name
      } : null,
      teamName: this.integrationDuplicateForm.value.selectedTeam?.teamName || this.entityDetails?.teamName,
      teamId: this.integrationDuplicateForm.value.selectedTeam?.id || this.entityDetails?.teamId,
    };

    if (this.isBulkFb) {
      payload.entityIds = this.selectedAccountId;
      delete payload?.entityId;
    }

    this.isBulkFb ? this.store?.dispatch?.(new UpdateUserAssignment(payload, null, true)) : this.store.dispatch(new UpdateUserAssignment(payload));
    this.hideAssignmentPopup();
    this.gridApi?.deselectAll();
  }

  convertMinutesToFormat(durationInMinutes: number): string {
    const hours = Math.floor(durationInMinutes / 60);
    const remainingMinutes = durationInMinutes % 60;

    const formattedHours = hours.toString().padStart(2, '0');
    const formattedMinutes = remainingMinutes.toString().padStart(2, '0');

    return `${formattedHours}:${formattedMinutes}:00`;
  }

  assignAccount() {
    if (this.isFbComponent || this.isBulkFb) {
      this.assignFbAccount();
      return;
    }
    // if (this.configForm.get('leadRotationEnabled').value) {
    //   if (!this.leadRotationAddGroupForm?.valid) {
    //     validateAllFormFields(this.leadRotationAddGroupForm);
    //     return;
    //   }
    // }
    if (this.getTotalPercentage() !== 100 && this.configForm.get('categoryType').value) {
      this._notificationService.warn('Warning', "Total percentage should be 100%");
      return;
    }
    if (this.assignedUser?.length && (!this.assignedPrimaryUsers?.length || this.assignedPrimaryUsers.length === 0)) {
      this.assignedPrimaryUsers = [...this.assignedUser];
    }

    if (!this.validateAssignmentForm())
      return;

    // if (!this.assignedUser?.length) {
    //   validateAllFormFields(this.integrationDuplicateForm);
    //   this._notificationService.warn('Warning', "Please select at least one user.");
    //   return;
    // }
    const checkedUsers = this.getCheckedUsers();
    const userAssignmentConfigurations = checkedUsers.map(userId => ({
      userId,
      percentage: this.configForm.get('categoryType').value ? this.userPercentages[userId] : 0
    }));

    const assignmentTypeMap: Record<string, UserAssignmentType> = {
      'users': UserAssignmentType.User,
      'teams': UserAssignmentType.Team,
      'projects': UserAssignmentType.Project,
      'properties': UserAssignmentType.Property
    };

    let createLeadRotationInfo;
    // if (this.configForm.get('leadRotationEnabled').value) {

    //   createLeadRotationInfo = {
    //     id: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.id : EMPTY_GUID,
    //     groupName: this.leadRotationAddGroupForm.get('teamName').value,
    //     assignmentGroup: checkedUsers,
    //     groupManager: this.leadRotationAddGroupForm.get('teamLeader').value,
    //     noOfRotation: this.leadRotationAddGroupForm.get('rotationNum').value,
    //     configuration: {
    //       id: this.entityDetails?.leadsAssignRotationInfo ? this.entityDetails?.leadsAssignRotationInfo?.configuration?.id : EMPTY_GUID,
    //       timeInterval: this.convertMinutesToFormat(Number(this.leadRotationAddGroupForm.get('rotationTime').value)),
    //       startTime: setTimeZoneTime(this.leadRotationAddGroupForm.get('shiftTimeFrom').value, this.userData?.timeZoneInfo?.baseUTcOffset),
    //       endTime: setTimeZoneTime(this.leadRotationAddGroupForm.get('shiftTimeTo').value, this.userData?.timeZoneInfo?.baseUTcOffset)
    //     },
    //     integrationAccountInfoId: this.selectedAccountId
    //   };
    // }
    let selectedIds: any
    this.selectedIntegrations = this.updatedIntegrationList?.filter(
      (item: any) => item.isSelected
    );
    selectedIds = this.selectedIntegrations?.map((node: any) =>
      node?.accountId
    );
    if (selectedIds?.length > 0) {
      let payload: any = {
        moduleId: this.moduleId,
        entityIds: selectedIds,
        userIds: this.canAllowSecondaryUsers ? (this.assignedPrimaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedPrimaryUsers) :
          (checkedUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : checkedUsers),
        secondaryUserIds: this.assignedSecondaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedSecondaryUsers,
        duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedDuplicateUser,
        isDuplicateAssignmentEnabled: this.canAllowDuplicates,
        isDualAssignmentEnabled: this.canAllowSecondaryUsers,
        shouldCreateMultipleDuplicates: !this.canAssignSequentially,
        userAssignmentConfigurations: userAssignmentConfigurations,
        // shouldConfigureLeadRotation: this.configForm.get('leadRotationEnabled').value,
        categoryType: this.configForm.get('categoryType').value ? AssignmentCategoryType.PercentageBased : AssignmentCategoryType.SequentialBased,
        userAssignmentType: assignmentTypeMap[this.configForm.get('assignmentType').value as string] || UserAssignmentType.User,
        // createLeadRotationInfo: createLeadRotationInfo,
        property: this.integrationDuplicateForm.value.selectedProperty?.id || this.entityDetails?.property?.id ? {
          id: this.integrationDuplicateForm.value.selectedProperty?.id || this.entityDetails?.property?.id,
          title: this.integrationDuplicateForm.value.selectedProperty?.title || this.entityDetails?.property?.title
        } : null,
        project: this.integrationDuplicateForm.value.selectedProject?.id || this.entityDetails?.project?.id ? {
          id: this.integrationDuplicateForm.value.selectedProject?.id || this.entityDetails?.project?.id,
          name: this.integrationDuplicateForm.value.selectedProject?.name || this.entityDetails?.project?.name
        } : null,
        teamName: this.integrationDuplicateForm.value.selectedTeam?.teamName || this.entityDetails?.teamName,
        teamId: this.integrationDuplicateForm.value.selectedTeam?.id || this.entityDetails?.teamId,
      };
      this.store.dispatch(new UpdateMultiUserAssignment(payload, 'Project'))
    } else {
      let payload: any = {
        entityId: this.selectedAccountId,
        userIds: this.canAllowSecondaryUsers ? (this.assignedPrimaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedPrimaryUsers) :
          (checkedUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : checkedUsers),
        secondaryUserIds: this.assignedSecondaryUsers?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedSecondaryUsers,
        duplicateUserIds: this.assignedDuplicateUser?.includes('selectedAllGroup') ? (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id) : this.assignedDuplicateUser,
        isDuplicateAssignmentEnabled: this.canAllowDuplicates,
        isDualAssignmentEnabled: this.canAllowSecondaryUsers,
        moduleId: this.moduleId,
        shouldCreateMultipleDuplicates: !this.canAssignSequentially,
        userAssignmentConfigurations: userAssignmentConfigurations,
        // shouldConfigureLeadRotation: this.configForm.get('leadRotationEnabled').value,
        categoryType: this.configForm.get('categoryType').value ? AssignmentCategoryType.PercentageBased : AssignmentCategoryType.SequentialBased,
        userAssignmentType: assignmentTypeMap[this.configForm.get('assignmentType').value as string] || UserAssignmentType.User,
        // createLeadRotationInfo: createLeadRotationInfo,
        property: this.integrationDuplicateForm.value.selectedProperty?.id || this.entityDetails?.property?.id ? {
          id: this.integrationDuplicateForm.value.selectedProperty?.id || this.entityDetails?.property?.id,
          title: this.integrationDuplicateForm.value.selectedProperty?.title || this.entityDetails?.property?.title
        } : null,
        project: this.integrationDuplicateForm.value.selectedProject?.id || this.entityDetails?.project?.id ? {
          id: this.integrationDuplicateForm.value.selectedProject?.id || this.entityDetails?.project?.id,
          name: this.integrationDuplicateForm.value.selectedProject?.name || this.entityDetails?.project?.name
        } : null,
        teamName: this.integrationDuplicateForm.value.selectedTeam?.teamName || this.entityDetails?.teamName,
        teamId: this.integrationDuplicateForm.value.selectedTeam?.id || this.entityDetails?.teamId,
      };

      this.store.dispatch(new UpdateUserAssignment(payload))
    }
    this.reset();
    this.hideAssignmentPopup();
  }

  hideAssignmentPopup() {
    this.isShowAssignModalChanged.emit(false);
    this.modalService.hide()
  }

  handleSelectAll(isAssignedUser: boolean = false) {
    const allUsers: string[] = (this.canAssignToAny ? this.allActiveUsers : this.activeUsers).map((user: any) => user?.id);
    if (this.assignedUser?.includes("selectedAllGroup")) {
      this.assignedUser = allUsers;
    }
    if (this.assignedDuplicateUser?.includes("selectedAllGroup")) {
      this.assignedDuplicateUser = allUsers;
    }
    if (this.assignedPrimaryUsers?.includes("selectedAllGroup")) {
      this.assignedPrimaryUsers = allUsers;
    }
    if (this.assignedSecondaryUsers?.includes("selectedAllGroup")) {
      this.assignedSecondaryUsers = allUsers;
    }

    if (isAssignedUser) {
      this.assignedPrimaryUsers = this.assignedUser;
    } else {
      this.assignedUser = this.assignedPrimaryUsers;
    }
  }

  sameAsSelectedUsersClicked(isPrimaryUser: boolean = false) {
    if (isPrimaryUser) {
      if (this.sameAsPrimaryUsers) {
        return;
      }
      if (!this.assignedPrimaryUsers?.length && this.assignedUser?.length) {
        this.assignedPrimaryUsers = [...this.assignedUser];
      }
      this.assignedSecondaryUsers = [...this.assignedPrimaryUsers];
      return;
    }

    if (this.sameAsSelectedUsers) {
      return;
    }
    this.assignedDuplicateUser = [...this.assignedUser];
  }

  reset() {
    this.updatedIntegrationList?.forEach((item) => (item.isSelected = false));
    this.assignedUser = [];
    this.checkedUsers = {};
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  resetIntegrationForm() {
    if (!this.canAllowSecondaryUsers) {
      this.assignedSecondaryUsers = [];
    }
    if (!this.canAllowDuplicates) {
      this.assignedDuplicateUser = [];
    }

    this.sameAsPrimaryUsers = false;
    this.sameAsSelectedUsers = false;
    this.sameAsAbove = false;
    this.listSelection = "original";
    // this.toggleAssignedUserValidation();
    this.revertValidation();
  }

  revertValidation() {
    const integrationDuplicateFormControlNames = ['assignedUser', 'assignedDuplicateUser'];
    integrationDuplicateFormControlNames?.forEach((controlName: string) => {
      this.integrationDuplicateForm.get(controlName).markAsPristine();
      this.integrationDuplicateForm.get(controlName).markAsUntouched();
      if (controlName !== 'assignedUser')
        this.integrationDuplicateForm.get(controlName).setErrors(null);
    });

    if (!this.canAllowSecondaryUsers) {
      const integrationDualOwnerFormControlNames = ['assignedSecondaryUsers', 'assignedDuplicateUser'];
      integrationDualOwnerFormControlNames?.forEach((controlName: string) => {
        this.integrationDualOwnerForm.get(controlName).markAsPristine();
        this.integrationDualOwnerForm.get(controlName).markAsUntouched();
        this.integrationDualOwnerForm.get(controlName).setErrors(null);
      });
    }

    this.selectedUserType = "Primary User(s)";
  }

  setListSelection() {
    // Sync users before determining list selection
    this.syncPrimaryAndAssignedUsers();

    // Preserve assigned users when switching between tabs
    if (this.assignedUser?.length && this.assignedPrimaryUsers?.length === 0) {
      this.assignedPrimaryUsers = [...this.assignedUser];
    }

    this.listSelection = (this.canAllowDuplicates && !this.canAllowSecondaryUsers) ?
      this.assignedUser?.length ? 'original' : 'duplicate' :
      this.assignedPrimaryUsers?.length ? 'primary' :
        this.assignedSecondaryUsers?.length ? 'secondary' : 'duplicate';
  }

  originalDuplicateListToggle(selection: string) {
    this.listSelection = selection;
    this.syncPrimaryAndAssignedUsers();
  }

  sameAsPrimarySecondaryUsersClicked() {
    if (this.sameAsAbove)
      return;
    this.assignedDuplicateUser = this.selectedUserType == 'Primary User(s)' ? [...this.assignedPrimaryUsers] : [...this.assignedSecondaryUsers];
  }

  syncPrimaryAndAssignedUsers() {
    if (this.canAllowSecondaryUsers) {
      if (this.assignedUser?.length && (!this.assignedPrimaryUsers?.length || this.assignedPrimaryUsers.length === 0)) {
        this.assignedPrimaryUsers = [...this.assignedUser];
      } else if (this.assignedPrimaryUsers?.length && (!this.assignedUser?.length || this.assignedUser.length === 0)) {
        this.assignedUser = [...this.assignedPrimaryUsers];
      }
    }
  }

  openConfirmDeleteModal(dataName: string, id: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeSelection(id);
        }
      });
    }
  }

  removeSelection(id: string) {
    if (this.isBulkAssignModel) {
      this.selectedIntegrations = this.selectedIntegrations?.filter((dataNodes: any) => dataNodes?.accountId !== id);
      if (this.selectedIntegrations?.length <= 0) this.modalService.hide();
    } else {
      const node = this.gridApi
        ?.getSelectedNodes()
        ?.filter((dataNodes: any) => dataNodes?.data?.id === id);
      this.gridApi?.deselectNode(node?.[0]);
      if (this.gridApi?.getSelectedNodes()?.length <= 0) this.modalService.hide();
    }
  }

  handleDuplicatesToggle() {
    const primaryUsers = [...this.assignedUser];
    this.resetIntegrationForm();
    // this.toggleAssignedUserValidation();

    if (primaryUsers.length > 0) {
      this.assignedUser = primaryUsers;

      if (!this.assignedPrimaryUsers?.length) {
        this.assignedPrimaryUsers = [...primaryUsers];
      }
    }

    this.setListSelection();
  }

  handleSecondaryUsersToggle() {
    const primaryUsers = [...this.assignedUser];
    this.resetIntegrationForm();
    // this.toggleAssignedUserValidation();
    if (primaryUsers.length > 0) {
      this.assignedUser = primaryUsers;
      if (!this.assignedPrimaryUsers?.length) {
        this.assignedPrimaryUsers = [...primaryUsers];
      }
    }
    this.setListSelection();
  }

  validateAssignmentForm() {
    if (this.selectedSectionLeadAssignment === 'AdditionalConfig') {
      if (this.canAllowDuplicates && !this.canAllowSecondaryUsers) {
        if (!this.integrationDuplicateForm?.valid) {
          validateAllFormFields(this.integrationDuplicateForm);
          return false;
        }
        if (this.assignedUser?.length == 1 && this.assignedDuplicateUser?.length == 1) {
          if (this.assignedUser?.[0] == this.assignedDuplicateUser?.[0]) {
            this._notificationService.warn('Warning', "Duplicate user assignment detected.");
            return false;
          }
        }
      } else if (this.canAllowDuplicates && this.canAllowSecondaryUsers) {
        if (!this.integrationDualOwnerForm?.valid) {
          validateAllFormFields(this.integrationDualOwnerForm);
          return false;
        }
        if (
          (this.assignedPrimaryUsers?.length == 1 && this.assignedSecondaryUsers?.length == 1) ||
          (this.assignedDuplicateUser?.length == 1 && this.assignedSecondaryUsers?.length == 1) ||
          (this.assignedDuplicateUser?.length == 1 && this.assignedPrimaryUsers?.length == 1)
        ) {
          if (
            (this.assignedPrimaryUsers?.[0] == this.assignedDuplicateUser?.[0] && this.assignedPrimaryUsers?.length == 1 && this.assignedDuplicateUser?.length == 1) ||
            (this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0] && this.assignedPrimaryUsers?.length == 1 && this.assignedSecondaryUsers?.length == 1) ||
            (this.assignedSecondaryUsers?.[0] == this.assignedDuplicateUser?.[0] && this.assignedSecondaryUsers?.length == 1 && this.assignedDuplicateUser?.length == 1)
          ) {
            this._notificationService.warn('Warning', "Duplicate user assignment detected.");
            return false;
          }
        }
      } else if (!this.canAllowDuplicates && this.canAllowSecondaryUsers) {
        if (!this.integrationDualOwnerForm.controls['assignedSecondaryUsers'].valid) {
          validateAllFormFields(this.integrationDualOwnerForm);
          return false;
        }
        if (this.assignedPrimaryUsers?.length && this.assignedSecondaryUsers?.length) {
          if (this.assignedPrimaryUsers?.length == 1 && this.assignedSecondaryUsers?.length == 1) {
            if (this.assignedPrimaryUsers?.[0] == this.assignedSecondaryUsers?.[0]) {
              this._notificationService.warn('Warning', "Duplicate user assignment detected.");
              return false;
            }
          }
          return true;
        }
      }
    }
    return true;
  }

  openConfirmModal(allowDuplicatesPopupRef: any, settingType: string) {
    this.allowDuplicatesPopupRef = this.modalService.show(allowDuplicatesPopupRef, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'allowDuplicateLeads':
        this.message =
          'To use this feature "Allow Lead Duplicates" must be enabled.';
        this.notes =
          'Please read the instructions clearly and proceed.';
        break;
      case 'allowSecondaryUsers':
        this.message =
          'To use this feature "Dual Lead Ownership" must be enabled.';
        this.notes =
          'Please read the instructions clearly and proceed.';
        break;
    }
  }

  closePopup() {
    this.allowDuplicatesPopupRef.hide();
  }

  goToGlobalConfig() {
    this.hideAssignmentPopup();
    this.closePopup();
    this.closeModal();
    this.router.navigate(['global-config', 'lead-settings']);
  }

  onSave() {
    if (this.IsAssignedCallLogsEnabled && this.selectedOption === 'users' && this.assignedUser?.length > 0) {
      if (!this.validatePercentages()) {
        return;
      }
    }

    if (this.selectedOption === 'users' && this.assignedUser?.length > 0) {
      this.initializeUserPercentages();
    }
  }

  initializeUserPercentages() {
    this.userPercentages = {};
    const users = Object.keys(this.checkedUsers).length > 0
      ? this.assignedUser.filter(userId => this.checkedUsers[userId])
      : this.assignedUser;

    if (users && users.length > 0) {
      const equalPercentage = Math.floor(100 / users.length);
      const totalForAllButLast = equalPercentage * (users.length - 1);
      const lastUserShare = 100 - totalForAllButLast;

      users.forEach((userId, index) => {
        if (index === users.length - 1) {
          this.userPercentages[userId] = lastUserShare;
        } else {
          this.userPercentages[userId] = equalPercentage;
        }
      });
    }
  }

  getTotalPercentage(): number {
    if (!this.assignedUser?.length) return 0;
    const checkedUsers = this.getCheckedUsers();
    let total = 0;
    checkedUsers.forEach(userId => {
      total += this.userPercentages[userId] || 0;
    });

    return total;
  }

  validatePercentages() {
    const total = this.getTotalPercentage();
    if (total > 200) {
      this._notificationService.warn('Warning', `Total percentage (${total}%) exceeds 200%. Please adjust values to equal 100%.`);
      return false;
    }
    if (total !== 100) {
      console.warn(`Total percentage is ${total}%, should be 100%`);
      return false;
    }
    return true;
  }

  onProjectSelectionChange(selectedProject: any) {
    this.assignedUser = [];
    if (selectedProject) {
      this.selectedProject = selectedProject;
      this.store.dispatch(
        new FetchUserAssignmentByEntity(this.selectedProject?.id)
      );

      this.store
        .select(getUserAssignmentByEntity)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          // if (data && data.userIds) {
          this.assignedUser = data?.userIds;
          this.selectedOption = 'projects';
          this.configForm.get('assignmentType').setValue('projects');
          this.initializeCheckedUsers();
          // }
        });
    }
  }

  onPropertySelectionChange(selectedProperty: any) {
    this.assignedUser = [];
    if (selectedProperty) {
      this.selectedProperty = selectedProperty;
      this.store.dispatch(
        new FetchPropertyAssignments(selectedProperty?.id)
      );
      this.store
        .select(getPropertyAssignments)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          // if (data && data.assignedTo) {
          this.assignedUser = data?.assignedTo;
          this.selectedOption = 'properties';
          this.configForm.get('assignmentType').setValue('properties');
          this.initializeCheckedUsers();
          // }
        });
    }
  }

  onTeamSelectionChange(selectedTeam: any) {
    this.assignedUser = [];
    if (selectedTeam) {
      this.selectedTeam = selectedTeam;
      const teamLeaderId = selectedTeam?.manager?.id;
      this.assignedUser = selectedTeam?.users
        ?.filter((user: any) => user.id !== teamLeaderId)
        ?.map((user: any) => user.id);

      const fullTeamData = this.teamsList.find(team => team.id === selectedTeam.id);
      if (fullTeamData) {
        this.selectedTeam = fullTeamData;
      }
      // this.updateLeadRotationForm();
      this.initializeCheckedUsers();
    } else {
      this.assignedUser = [];
      this.leadRotationAddGroupForm.reset();
      this.selectedTeam = null;
    }
  }

  timeToMinutes(timeString: string): number {
    if (timeString) {

      const [hours, minutes, seconds] = timeString?.split(':').map(Number);

      const totalMinutes = hours * 60 + minutes;

      return totalMinutes;
    }
    return null;
  }

  // updateLeadRotationForm() {
  //   if (this.leadRotationAddGroupForm) {
  //     const teamLeaderId = this.selectedTeam.manager?.id;

  //     this.leadRotationAddGroupForm.patchValue({
  //       teamName: this.selectedTeam ? this.selectedTeam.teamName : null,
  //       teamLeader: teamLeaderId
  //     });

  //     if (teamLeaderId && this.assignedUser && this.assignedUser.includes(teamLeaderId)) {
  //       this.assignedUser = this.assignedUser.filter(userId => userId !== teamLeaderId);
  //       delete this.checkedUsers[teamLeaderId];
  //       this.initializeCheckedUsers();
  //     }
  //     this.filterTeamLeader();
  //   }
  // }

  filterTeamLeader() {
    const sourceUsers = this.canAssignToAny ? this.allActiveUsers : this.activeUsers;
    if (!sourceUsers) {
      this.teamLeaders = [];
      return;
    }
    this.teamLeaders = [...sourceUsers];
    if (this.assignedUser && this.assignedUser.length > 0) {
      this.teamLeaders = this.teamLeaders.filter(user => !this.assignedUser.includes(user.id));
    }
  }

  initializeCheckedUsers() {
    this.checkedUsers = {};

    if (this.assignedUser && this.assignedUser.length > 0) {
      this.assignedUser.forEach(userId => {
        this.checkedUsers[userId] = true;
      });
    }
  }

  onUserCheckChange(userId: string, isChecked: boolean) {
    this.checkedUsers[userId] = isChecked;
    if (this.configForm.get('categoryType').value) {
      const checkedUsers = this.getCheckedUsers();

      if (checkedUsers.length === 0) {
        this.userPercentages = {};
        return;
      }
      const equalPercentage = Math.floor(100 / checkedUsers.length);
      const totalForAllButLast = equalPercentage * (checkedUsers.length - 1);
      const lastUserShare = 100 - totalForAllButLast;
      checkedUsers.forEach((id, index) => {
        if (index === checkedUsers.length - 1) {
          this.userPercentages[id] = lastUserShare;
        } else {
          this.userPercentages[id] = equalPercentage;
        }
      });
    }
  }

  getCheckedUsers(): string[] {
    return this.assignedUser.filter(userId => this.checkedUsers[userId]);
  }

  handlePercentageChange(userId: string, newPercentage: number) {
    const oldPercentage = this.userPercentages[userId] || 0;

    newPercentage = Math.min(100, Math.max(0, Math.round(newPercentage)));

    this.userPercentages[userId] = newPercentage;

    const checkedUsers = this.getCheckedUsers();
    if (checkedUsers.length <= 1) return;

    const remainingPercentage = 100 - newPercentage;

    const otherUsers = checkedUsers.filter(id => id !== userId);
    if (otherUsers.length === 0) return;

    const equalShare = Math.floor(remainingPercentage / otherUsers.length);
    const totalForAllButLast = equalShare * (otherUsers.length - 1);
    const lastUserShare = remainingPercentage - totalForAllButLast;
    otherUsers.forEach((id, index) => {
      if (index === otherUsers.length - 1) {
        this.userPercentages[id] = lastUserShare;
      } else {
        this.userPercentages[id] = equalShare;
      }
    });
  }

  closeModal() {
    this.modalRef.hide();
    this.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}


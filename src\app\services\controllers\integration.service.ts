import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { LeadSource } from 'src/app/app.enum';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class IntegrationService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string;
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = ``;
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'integration';
  }

  getAccountDetailsById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/integrationaccountinfo/${id}`);
  }

  getIVRDetailsById(id: string) {
    return this.http.get(`${this.serviceBaseUrl}/ivr?Id=${id}`);
  }

  getExcelFile(resource: any) {
    return this.http.post(`${this.serviceBaseUrl}`, resource);
  }

  googleAdsLandingPage(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/googleads/landingpageexcel`,
      payload
    );
  }

  googleAdsLeadForm(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/googleads/leadformexcel`,
      payload
    );
  }

  reDownloadExcel(id: any) {
    return this.http.get(`${this.serviceBaseUrl}/${id}`, {
      responseType: 'blob',
      observe: 'response',
    });
  }

  getTotalCount() {
    return this.http.get(`${this.serviceBaseUrl}/totalcount`);
  }

  getIVRAccountCount() {
    return this.http.get(`${this.serviceBaseUrl}/ivr/count`);
  }

  gmailIntegration(resource: any) {
    return this.http.post(
      `${env.baseURL}${env.apiURL}integration/gmail`,
      resource
    );
  }

  IntegrationEmail(payload: any) {
    return this.http.post(
      `${env.baseURL}${env.apiURL}integration/email`,
      payload
    );
  }

  sendListOfAccounts(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/facebook/accounts`, payload);
  }

  sendFbMappingDetails(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/facebook/forms`, payload);
  }

  getAllSubscribedForms(page: number, pagesize: number) {
    let params = new HttpParams()
      .set('PageNumber', page.toString())
      .set('PageSize', pagesize.toString());
    return this.http.get(
      `${this.serviceBaseUrl}/facebook/forms?${params.toString()}`
    );
  }

  ivrIntegration(resource: any) {
    return this.http.post(`${this.serviceBaseUrl}/servetel`, resource);
  }

  commonIvrIntegration(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/ivr/create/with-params`,
      payload
    );
  }

  clickToCall(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/servetel/clicktocall`,
      payload
    );
  }

  commonClickToCall(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/common/clicktocall/config`,
      payload
    );
  }

  updateIVRAccount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/ivr/update`, payload);
  }

  makePrimary(id: string) {
    return this.http.put(`${this.serviceBaseUrl}/makeprimary/${id}`, null);
  }

  getAgents() {
    return this.http.get(`${this.serviceBaseUrl}/servetel/agents`);
  }

  getVirtualNos() {
    return this.http.get(`${this.serviceBaseUrl}/ivr/virtual-numbers`);
  }

  getIVRVNAssignment(leadId: string) {
    let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    let params = new HttpParams()
      .set('LeadId', leadId.toString())
      .set('UserId', userId.toString());
    return this.http.get(
      `${this.serviceBaseUrl
      }/ivr/virtual-number/assignment-check?${params.toString()}`
    );
  }

  getIvrServiceProviders() {
    return this.http.get(`${this.serviceBaseUrl}/ivr/service-providers`);
  }

  getAllFacebookAccounts(page: number, pagesize: number) {
    let params = new HttpParams()
      .set('PageNumber', page.toString())
      .set('PageSize', pagesize.toString());
    return this.http.get(
      `${this.serviceBaseUrl}/facebook/account?${params.toString()}`
    );
  }

  addFacebookAccount(resource: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/facebook/account/console`,
      resource
    );
  }

  getGoogleAccountDetails(payload: {
    LeadSource?: number;
    SearchByName?: string;
    PageSize?: number;
    PageNumber?: number;
  }): Observable<any> {
    let params = new HttpParams();

    Object.entries(payload || {}).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get(`${this.serviceBaseUrl}/googleads/accounts?${params.toString()}`);
  }

  getIntegrationList(payload: {
    LeadSource?: number;
    SearchByName?: string;
    PageSize?: number;
    PageNumber?: number;
  }): Observable<any> {
    let params = new HttpParams();

    Object.entries(payload || {}).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get(`${this.serviceBaseUrl}?${params.toString()}`);
  }

  deleteGoogleAd(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/googleads/${id}`);
  }

  deleteFBAccount(id: string) {
    return this.http.delete(`${this.serviceBaseUrl}/facebook/account/${id}`);
  }

  getGoogleAdsLeads(id: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/googleadsintegrationaccountinfo/${id}`
    );
  }

  automateIntegration(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/automate`, payload);
  }

  automateFBAdIntegration(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/automate/fb-ad`, payload);
  }

  automateFBFormIntegration(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/automate/fb-form`, payload);
  }

  ToggleFBSubscription(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/fb/toggle-subscription`,
      payload
    );
  }

  toggleFbSubscription(accountId: string, isSubscribed: boolean) {
    return this.http.post(
      `${this.serviceBaseUrl}/fb/toggle-subscription/new`,
      { accountId, isSubscribed }
    );
  }

  checkFbSubscriptionStatus(accountId: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/fb/subscription?AccountId=${accountId}`
    );
  }

  syncAdsOfAnFBAccount(accountId: string) {
    return this.http.get(`${this.serviceBaseUrl}/fb/sync/${accountId}`);
  }

  getAssignmentDetails(id: string, source: LeadSource) {
    let params = new HttpParams().set('source', source);
    return this.http.get(
      `${this.serviceBaseUrl}/assignment/${id}?${params.toString()}`
    );
  }

  agencyName(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/agencyname`, payload);
  }

  getAgencyNameList() {
    return this.http.get(`${this.serviceBaseUrl}/agencynames`);
  }

  updateProjects(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/projects`, payload);
  }

  getProjects(id: string, source: LeadSource) {
    let params = new HttpParams().set('source', source);
    return this.http.get(
      `${this.serviceBaseUrl}/projects/${id}?${params.toString()}`
    );
  }

  getAllFbAccounts() {
    return this.http.get(`${this.serviceBaseUrl}/facebook/account/new`);
  }

  addJustAccount(resource: any) {
    return this.http.post(`${this.serviceBaseUrl}/justlead/account`, resource);
  }

  updateJustAccount(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/justlead/account`, resource);
  }

  addCFAccount(resource: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/commonfloor/account`,
      resource
    );
  }

  updateCFAccount(resource: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/commonfloor/account`,
      resource
    );
  }

  fetchFbBulkLeadsFetch(payload: any) {
    const filteredPayload = Object.keys(payload)
      .filter((key: any) => payload[key] !== null && payload[key] !== undefined)
      .reduce((obj: any, key: any) => {
        obj[key] = payload[key];
        return obj;
      }, {});

    let params = new HttpParams();
    for (const key in filteredPayload) {
      if (filteredPayload.hasOwnProperty(key)) {
        params = params.append(key, filteredPayload[key]);
      }
    }

    return this.http.post(
      `${this.serviceBaseUrl}/fb/bulk-fetch?${params.toString()}`,
      {}
    );
  }

  addMicrositeLead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/microsite`, payload);
  }

  addProjectMicrositeLead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/project/microsite`, payload);
  }

  fetchIVRList() {
    return this.httpClient.get(`${this.APIUrl}/ivr/acc/with-mapping`);
  }

  getExportFacebookStatus(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/fb/bulk/fetch/status?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  webhookaccounts(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/webhook/push/properties`,
      payload
    );
  }

  addWebhook() {
    return this.httpClient.get(
      `${this.serviceBaseUrl}/webhook/payload/parameters`
    );
  }

  addPropertyFinderAccount(resource: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/property-finder/account`,
      resource
    );
  }

  updatePropertyFinderAccount(resource: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/property-finder/account`,
      resource
    );
  }

  addBayutAccount(resource: any) {
    return this.http.post(`${this.serviceBaseUrl}/bayut/account`, resource);
  }

  updateBayutAccount(resource: any) {
    return this.http.put(`${this.serviceBaseUrl}/bayut/account`, resource);
  }

  updateWebhookAccount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/update/webhook`, payload);
  }

  getWebhookAccount(id: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/webhook/accountinfo/?id=${id}`
    );
  }

  doesAccountNameExists(Accountname: string, Source: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/checkaccountname?AccountName=${Accountname}&Source=${Source}`
    );
  }

  addDubizzleAccount(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/dubizzle/account`, payload);
  }

  updateDubizzleAccount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/dubizzle/account`, payload);
  }

  updatePixelAccount(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/pixel-confguration`, payload);
  }

  getFacebookMarketing(payload: any) {
    // Create the request body with all the payload data
    const requestBody: any = {
      accountId: payload.accountId,
      campaignAds: payload.campaignAds
    };

    // Add CPL tracking parameters if they exist and are not null
    if (payload.DateRange !== undefined && payload.DateRange !== null) {
      requestBody.DateRange = payload.DateRange;
    }
    if (payload.IsCustomDate !== undefined && payload.IsCustomDate !== null) {
      requestBody.IsCustomDate = payload.IsCustomDate;
    }
    if (payload.FromDate !== undefined && payload.FromDate !== null) {
      requestBody.FromDate = payload.FromDate;
    }
    if (payload.ToDate !== undefined && payload.ToDate !== null) {
      requestBody.ToDate = payload.ToDate;
    }

    return this.http.post(`${this.serviceBaseUrl}/facebook/marketing`, requestBody);
  }

  getFbAccountForms() {
    return this.http.get(`${this.serviceBaseUrl}/facebook/account-forms`);
  }
}

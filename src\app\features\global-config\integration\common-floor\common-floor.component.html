<form class="text-coal" [ngClass]="{'pe-none blinking': isIntegrationLoading}">
  <!-- imge section -->
  <div class="p-20 flex-between">
    <div class="align-center">
      <div class="icon ic-triangle rotate-90 ic-xxs ic-coal cursor-pointer p-8 mr-4" routerLink='/global-config'>
      </div>
      <img [type]="'leadrat'" [appImage]="image" alt="img" />
    </div>
    <div>
      <button type="button" *ngIf="canAdd" class="btn-coal" (click)="addNewAccount(addAccount)"
        id="btnAddAccount" data-automate-id="btnAddAccount">
        {{ 'SIDEBAR.add' | translate }} {{'SIDEBAR.account' | translate}}</button>
    </div>
  </div>
  <!-- no account -->
  <ng-container *ngIf="!updatedIntegrationList?.length && !searchTerm else addAndListingPage">
    <div
      *ngIf="displayName === 'Common Floor' || displayName === 'Property Finder' || displayName === 'Bayut' || displayName === 'Dubizzle'; else justLead"
      class="px-20 mt-20 justify-center-col">
      <div class="align-center">
        <div class="d-flex">
          <div class="align-center-col">
            <div class="dot dot-x-xxl bg-pearl cursor-pointer">
              <span class="icon ic-download ic-lg ic-coal"></span>
            </div>
            <div class="border-left-dotted h-60"></div>
          </div>
          <p class="text-coal fw-600 ml-20 mt-20">
            {{ 'INTEGRATION.download-excel' | translate }}
          </p>
        </div>
      </div>
      <div class="align-center">
        <div class="d-flex">
          <div class="align-center-col">
            <div class="dot dot-x-xxl bg-pearl cursor-pointer">
              <span class="icon ic-share ic-lg ic-coal"></span>
            </div>
            <div class="border-left-dotted h-60"></div>
          </div>
          <div class="text-gray ml-20 mt-20">
            <p>
              <span class="text-coal">Share it with your</span>
              <span class="fw-600 text-coal"> {{ displayName }}</span>
              {{ 'INTEGRATION.share-message2' | translate }}
              <span class="fw-600 text-coal">{{ displayName }}</span>
              {{ 'INTEGRATION.share-message3' | translate }} {{getAppName()}}
              {{'INTEGRATION.share-message4' | translate }}
            </p>
          </div>
        </div>
      </div>
      <div class="align-center">
        <div class="d-flex">
          <div class="align-center-col">
            <div class="dot dot-x-xxl bg-pearl cursor-pointer">
              <span class="icon ic-connection ic-lg ic-coal"></span>
            </div>
          </div>
          <div class="text-gray ml-20 mt-20">
            <p>{{ 'INTEGRATION.connection-message' | translate }}</p>
          </div>
        </div>
      </div>
    </div>
    <ng-template #justLead>
      <div class="mt-20 px-20">
        <h4 class="fw-600 mb-10">User Registration Details For Pull:</h4>
        <h5 class="mb-8">To integrate your user registration with the JustLead application, we kindly
          ask you to
          provide the
          following information:</h5>
        <div class="mb-8"><span class="fw-600 header-5">User ID:</span> This will be your unique
          identifier
          within the JustLead application.</div>
        <div class="mb-8"><span class="fw-600 header-5">User Name:</span> Please enter the username you
          currently use for your JustLead account.</div>
        <div class="mb-8"><span class="fw-600 header-5">Password:</span> Enter the password to
          authenticate your
          JustLead account.</div>
        <div>Please ensure that you enter the same User ID, User Name, and Password that you currently
          use to
          log into your existing JustLead account. This will ensure a seamless integration process and
          allow
          you to access your Leads with your existing login details.</div>
      </div>
    </ng-template>
  </ng-container>
  <!-- add account -->
  <ng-template #addAccount>
    <div class="bg-dark w-100 px-16 py-12 text-white flex-between">
      <div class="align-center">
        <div class="icon ic-envelope-solid ic-large mr-8"></div>
        <h4 class="fw-semi-bold">API Email Integration </h4>
      </div>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
    </div>
    <div class="w-100">
      <div [ngSwitch]="displayName">
        <div class="py-20">
          <div *ngSwitchCase="'Common Floor'">
            <form [formGroup]="integrateForm" (keydown.enter)="onEnterKey($event)">
              <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
                <div class="box-radio-lg">
                  <div class="field-label header-4 mt-0">Select Option</div>
                  <div class="flex-center mt-10 ip-flex-col">
                    <ng-container *ngIf="!editCred">
                      <input type="radio" class="btn-check" name="apiType" id="push" autocomplete="off"
                        formControlName="apiType" value="push">
                      <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="push">PUSH</label>
                    </ng-container>
                    <input type="radio" class="btn-check" name="apiType" id="pull" autocomplete="off"
                      formControlName="apiType" value="pull">
                    <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="pull">PULL
                    </label>
                  </div>
                </div>
                <div>
                  <div for="inpCFAcc" class="field-label-req">Account Name
                  </div>
                  <form-errors-wrapper label="Account Name" [control]="integrateForm.controls['accountName']">
                    <input type="text" required formControlName="accountName" appDebounceInput [debounceTime]="500"
                      (debounceEvent)="doesAccountNameExists()" name="accountName" id="inpCFAcc"
                      data-automate-id="inpCFAcc" autocomplete="off" placeholder="ex. Manasa Pampana" />
                  </form-errors-wrapper>
                </div>
                <div class="field-label">Login Id/Login Email</div>
                <form-errors-wrapper label="Login Id/Login Email" [control]="integrateForm.controls['loginEmail']">
                  <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
                </form-errors-wrapper>
                <div class="field-label-req" *ngIf="!editCred || integrationToRecipients()?.controls?.length > 0">
                  Relationship Manager Email</div>
                <form-errors-wrapper label="Relationship Manager Email"
                  [control]="integrateForm?.controls['toRecipients']" [hidden]="editCred">
                  <div class="flex-between position-relative">
                    <input type="text" required placeholder="ex. <EMAIL>" #emailToInput id="emailToInput"
                      class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false" autocomplete="off"
                      (keyup.enter)="addInputField(emailToInput.value, emailToInput, integrationToRecipients())"
                      (input)="handleEmailValidation(integrateForm, emailToInput, 'toRecipients')"
                      [ngClass]="(emailToInput.value && !validateEmail(emailToInput.value)) || (integrateForm.get('toRecipients').touched && !integrationToRecipients().length && !emailToInput.value) ? 'border-red-800': ''" />
                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailToInput.value && !validateEmail(emailToInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailToInput.value, emailToInput, integrationToRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                  </div>
                  <div *ngIf="emailToInput.value && !validateEmail(emailToInput.value)"
                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                    Enter a valid Email ID.
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="integrationToRecipients()?.controls?.length">
                  <div *ngFor="let email of integrationToRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, integrationToRecipients(), emailToInput)"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || integrationCcRecipients()?.controls?.length > 0">Additional
                  Email</div>
                <form-errors-wrapper label="Additional Email" [control]="integrateForm.controls['ccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailCcInput id="emailCcInput"
                      class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false" autocomplete="off"
                      (keyup.enter)="addInputField(emailCcInput.value, emailCcInput, integrationCcRecipients())"
                      [ngClass]="emailCcInput.value && !validateEmail(emailCcInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailCcInput.value && !validateEmail(emailCcInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailCcInput.value, emailCcInput, integrationCcRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailCcInput.value && !validateEmail(emailCcInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="integrationCcRecipients()?.controls?.length">
                  <div *ngFor="let email of integrationCcRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, integrationCcRecipients())"></span>
                  </div>
                </div>

                <div class="field-label" *ngIf="!editCred || integrationBccRecipients()?.controls?.length > 0">Bcc Email
                </div>
                <form-errors-wrapper label="Bcc Email" [control]="integrateForm.controls['bccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailBccInput id="emailBccInput"
                      class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false" autocomplete="off"
                      (keyup.enter)="addInputField(emailBccInput.value, emailBccInput, integrationBccRecipients())"
                      [ngClass]="emailBccInput.value && !validateEmail(emailBccInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailBccInput.value && !validateEmail(emailBccInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailBccInput.value, emailBccInput, integrationBccRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailBccInput.value && !validateEmail(emailBccInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="integrationBccRecipients()?.controls?.length">
                  <div *ngFor="let email of integrationBccRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, integrationBccRecipients())"></span>
                  </div>
                </div>
                <div *ngIf="integrateForm.controls['apiType'].value == 'pull'">
                  <div for="inpSecretId" class="field-label-req">Secret Id</div>
                  <form-errors-wrapper label="Secret Id" [control]="integrateForm.controls['secretId']">
                    <input type="text" formControlName="secretId" name="secretId" id="inpSecretId"
                      data-automate-id="inpSecretId" autocomplete="off" placeholder="enter....." />
                  </form-errors-wrapper>
                  <div for="inpSecretKey" class="field-label-req">Secret Key</div>
                  <form-errors-wrapper label="Secret Key" [control]="integrateForm.controls['secretKey']">
                    <input type="text" formControlName="secretKey" name="secretKey" id="inpSecretKey"
                      data-automate-id="inpSecretKey" autocomplete="off" placeholder="enter....." />
                  </form-errors-wrapper>
                </div>
              </div>
              <div class="flex-end p-16 box-shadow-20"> <button type="button" class="btn-gray mr-20"
                  (click)="closeModal()">{{
                  'BUTTONS.cancel' | translate }}</button><button type="button" class="btn-coal"
                  (click)="saveData()">
                  {{ integrateForm.controls['apiType'].value == 'pull' ? 'Save' :
                  'Add Account' }}
                </button>
              </div>
            </form>
          </div>
          <div *ngSwitchCase="'Just Lead'">
            <form [formGroup]="addJustLeadForm" (keydown.enter)="onEnterKey($event)">
              <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
                <div class="box-radio-lg">
                  <div class="field-label header-4 mt-0">Select Option</div>
                  <div class="flex-center mt-10 ip-flex-col">
                    <ng-container *ngIf="!editCred">
                      <input type="radio" class="btn-check" name="apiType" id="push" autocomplete="off"
                        formControlName="apiType" value="push">
                    </ng-container>
                    <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="push">PUSH</label>
                    <input type="radio" class="btn-check" name="apiType" id="pull" autocomplete="off"
                      formControlName="apiType" value="pull">
                    <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="pull">PULL
                    </label>
                  </div>
                </div>
                <div>
                  <div for="inpCFAcc" class="field-label-req">Account Name
                  </div>
                  <form-errors-wrapper label="Account Name" [control]="addJustLeadForm.controls['accountName']">
                    <input type="text" required formControlName="accountName" appDebounceInput [debounceTime]="500"
                      (debounceEvent)="doesAccountNameExists()" name="accountName" id="inpCFAcc"
                      data-automate-id="inpCFAcc" autocomplete="off" placeholder="ex. Manasa Pampana" />
                  </form-errors-wrapper>
                </div>
                <div class="field-label">Login Id/Login Email</div>
                <form-errors-wrapper label="Login Id/Login Email" [control]="addJustLeadForm.controls['loginEmail']">
                  <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
                </form-errors-wrapper>
                <div class="field-label-req" *ngIf="!editCred || justLeadToRecipients()?.controls?.length > 0">
                  Relationship Manager Email</div>
                <form-errors-wrapper label="Relationship Manager Email"
                  [control]="addJustLeadForm?.controls['toRecipients']" [hidden]="editCred">
                  <div class="flex-between position-relative">
                    <input type="text" required placeholder="ex. <EMAIL>" #emailJustLeadToInput
                      id="emailJustLeadToInput" class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false"
                      autocomplete="off"
                      (keyup.enter)="addInputField(emailJustLeadToInput.value, emailJustLeadToInput, justLeadToRecipients())"
                      (input)="handleEmailValidation(addJustLeadForm, emailJustLeadToInput, 'toRecipients')"
                      [ngClass]="(emailJustLeadToInput.value && !validateEmail(emailJustLeadToInput.value)) || (addJustLeadForm.get('toRecipients').touched && !justLeadToRecipients().length && !emailJustLeadToInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailJustLeadToInput.value && !validateEmail(emailJustLeadToInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailJustLeadToInput.value, emailJustLeadToInput, justLeadToRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                  </div>
                  <div *ngIf="emailJustLeadToInput.value && !validateEmail(emailJustLeadToInput.value)"
                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                    Enter a valid Email ID.
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="justLeadToRecipients()?.controls?.length">
                  <div *ngFor="let email of justLeadToRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, justLeadToRecipients(), emailJustLeadToInput)"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || justLeadCcRecipients()?.controls?.length > 0">Additional
                  Email</div>
                <form-errors-wrapper label="Additional Email" [control]="addJustLeadForm.controls['ccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailJustLeadCcInput id="emailJustLeadCcInput"
                      class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false" autocomplete="off"
                      (keyup.enter)="addInputField(emailJustLeadCcInput.value, emailJustLeadCcInput, justLeadCcRecipients())"
                      [ngClass]="emailJustLeadCcInput.value && !validateEmail(emailJustLeadCcInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailJustLeadCcInput.value && !validateEmail(emailJustLeadCcInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailJustLeadCcInput.value, emailJustLeadCcInput, justLeadCcRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailJustLeadCcInput.value && !validateEmail(emailJustLeadCcInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="justLeadCcRecipients()?.controls?.length">
                  <div *ngFor="let email of justLeadCcRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, justLeadCcRecipients())"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || justLeadBccRecipients()?.controls?.length > 0">Bcc Email
                </div>
                <form-errors-wrapper label="Bcc Email" [control]="addJustLeadForm.controls['bccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailJustLeadBccInput id="emailJustLeadBccInput"
                      class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false" autocomplete="off"
                      (keyup.enter)="addInputField(emailJustLeadBccInput.value, emailJustLeadBccInput, justLeadBccRecipients())"
                      [ngClass]="emailJustLeadBccInput?.value && !validateEmail(emailJustLeadBccInput?.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailJustLeadBccInput?.value && !validateEmail(emailJustLeadBccInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailJustLeadBccInput.value, emailJustLeadBccInput, justLeadBccRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailJustLeadBccInput.value && !validateEmail(emailJustLeadBccInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="justLeadBccRecipients()?.controls?.length">
                  <div *ngFor="let email of justLeadBccRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, justLeadBccRecipients())"></span>
                  </div>
                </div>
                <div *ngIf="addJustLeadForm.controls['apiType'].value == 'pull'">
                  <div for="inpUserId" class="field-label-req">User ID</div>
                  <form-errors-wrapper label="User ID" [control]="addJustLeadForm.controls['userId']">
                    <input type="text" formControlName="userId" name="userId" id="inpUserId"
                      data-automate-id="inpUserId" autocomplete="off" placeholder="enter....." />
                  </form-errors-wrapper>
                  <div class="field-label-req">{{ 'AUTH.user-name' | translate }}</div>
                  <form-errors-wrapper [control]="addJustLeadForm.controls['userName']"
                    label="{{ 'AUTH.user-name' | translate }}">
                    <input type="text" required formControlName="userName" name="userName" autocomplete="off"
                      id="inpuserName" data-automate-id="inpuserName" placeholder="enter.....">
                  </form-errors-wrapper>
                  <div class="field-label-req">{{ 'AUTH.password' | translate }}</div>
                  <form-errors-wrapper [control]="addJustLeadForm.controls['password']"
                    label="{{ 'AUTH.password' | translate }}">
                    <input [type]="isShowPassword ? 'text' : 'password'" required autocomplete="new-password"
                      formControlName="password" id="inpJustLeadPassword" data-automate-id="inpJustLeadPassword"
                      placeholder="enter.....">
                    <a class="icon ic-gray cursor-pointer position-absolute top-10 right-10"
                      [ngClass]="isShowPassword ? 'ic-eye-slash' : 'ic-eye-solid'" id="passwordhide"
                      data-automate-id="passwordhide" (click)="isShowPassword = !isShowPassword"></a>
                  </form-errors-wrapper>
                </div>
              </div>
              <div class="flex-end p-16 box-shadow-20">
                <div class="btn-gray mr-20" (click)="closeModal()">{{
                  'BUTTONS.cancel' | translate }}</div>
                <div class="btn-coal" type="button" (click)="saveData()">
                  {{ addJustLeadForm.controls['apiType'].value == 'pull' ? 'Save' :
                  'Add Account' }}
                </div>
              </div>
            </form>
          </div>
          <div *ngSwitchCase="'Property Finder'">
            <form [formGroup]="propertyFinderForm" (keydown.enter)="onEnterKey($event)">
              <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
                <div class="box-radio-lg">
                  <div class="field-label header-4 mt-0">Select Option</div>
                  <div class="flex-center mt-10 ip-flex-col">
                    <ng-container *ngIf="!editCred">
                      <input type="radio" class="btn-check" name="apiType" id="push" autocomplete="off"
                        formControlName="apiType" value="push">
                      <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="push">PUSH</label>
                    </ng-container>
                    <input type="radio" class="btn-check" name="apiType" id="pull" autocomplete="off"
                      formControlName="apiType" value="pull">
                    <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="pull">PULL
                    </label>
                  </div>
                </div>
                <div>
                  <div for="inpCFAcc" class="field-label-req">Account Name
                  </div>
                  <form-errors-wrapper label="Account Name" [control]="propertyFinderForm.controls['accountName']">
                    <input type="text" required formControlName="accountName" appDebounceInput [debounceTime]="500"
                      (debounceEvent)="doesAccountNameExists()" name="accountName" id="inpCFAcc"
                      data-automate-id="inpCFAcc" autocomplete="off" placeholder="ex. Manasa Pampana" />
                  </form-errors-wrapper>
                </div>
                <div class="field-label">Login Id/Login Email</div>
                <form-errors-wrapper label="Login Id/Login Email" [control]="propertyFinderForm.controls['loginEmail']">
                  <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
                </form-errors-wrapper>
                <!-- <div class="field-label-req" *ngIf="!editCred || propertyFinderToRecipients()?.controls?.length > 0">
                  Relationship Manager Email</div>
                <form-errors-wrapper label="Relationship Manager Email"
                  [control]="propertyFinderForm?.controls['toRecipients']" [hidden]="editCred">
                  <div class="flex-between position-relative">
                    <input type="text" required placeholder="ex. <EMAIL>" #emailPropertyFinderToInput
                      id="emailPropertyFinderToInput" class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false"
                      autocomplete="off"
                      (keyup.enter)="addInputField(emailDubizzleToInput.value, emailPropertyFinderToInput, propertyFinderToRecipients())"
                      (input)="handleEmailValidation(propertyFinderForm, emailPropertyFinderToInput, 'toRecipients')"
                      [ngClass]="(emailPropertyFinderToInput.value && !validateEmail(emailPropertyFinderToInput.value)) || (propertyFinderForm.get('toRecipients').touched && !propertyFinderToRecipients().length && !emailPropertyFinderToInput.value) ? 'border-red-800': ''" />
                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailPropertyFinderToInput.value && !validateEmail(emailPropertyFinderToInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailPropertyFinderToInput.value, emailPropertyFinderToInput, propertyFinderToRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                  </div>
                  <div *ngIf="emailPropertyFinderToInput.value && !validateEmail(emailPropertyFinderToInput.value)"
                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                    Enter a valid Email ID.
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="propertyFinderToRecipients()?.controls?.length">
                  <div *ngFor="let email of propertyFinderToRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, propertyFinderToRecipients(), emailPropertyFinderToInput)"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || propertyFinderCcRecipients()?.controls?.length > 0">
                  Additional Email</div>
                <form-errors-wrapper label="Additional Email" [control]="propertyFinderForm.controls['ccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailPropertyFinderCcInput
                      id="emailPropertyFinderCcInput" class="outline-0 padd-r pr-36" [readonly]="editCred ? true: false"
                      autocomplete="off"
                      (keyup.enter)="addInputField(emailPropertyFinderCcInput.value, emailPropertyFinderCcInput, propertyFinderCcRecipients())"
                      [ngClass]="emailPropertyFinderCcInput.value && !validateEmail(emailPropertyFinderCcInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailPropertyFinderCcInput.value && !validateEmail(emailPropertyFinderCcInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailPropertyFinderCcInput.value, emailPropertyFinderCcInput, propertyFinderCcRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailPropertyFinderCcInput.value && !validateEmail(emailPropertyFinderCcInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="propertyFinderCcRecipients()?.controls?.length">
                  <div *ngFor="let email of propertyFinderCcRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, propertyFinderCcRecipients())"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || propertyFinderBccRecipients()?.controls?.length > 0">Bcc
                  Email</div>
                <form-errors-wrapper label="Bcc Email" [control]="propertyFinderForm.controls['bccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailPropertyFinderBccInput
                      id="emailPropertyFinderBccInput" class="outline-0 padd-r pr-36" autocomplete="off"
                      [readonly]="editCred ? true: false"
                      (keyup.enter)="addInputField(emailPropertyFinderBccInput.value, emailPropertyFinderBccInput, propertyFinderBccRecipients())"
                      [ngClass]="emailPropertyFinderBccInput?.value && !validateEmail(emailPropertyFinderBccInput?.value) ? 'border-red-800': ''" />
                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailPropertyFinderBccInput?.value && !validateEmail(emailPropertyFinderBccInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailPropertyFinderBccInput.value, emailPropertyFinderBccInput, propertyFinderBccRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailPropertyFinderBccInput.value && !validateEmail(emailPropertyFinderBccInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="propertyFinderBccRecipients()?.controls?.length">
                  <div *ngFor="let email of propertyFinderBccRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, propertyFinderBccRecipients())"></span>
                  </div>
                </div> -->
                <div *ngIf="propertyFinderForm.controls['apiType'].value == 'pull'">
                  <div for="inpRequestType" class="field-label-req">Request Type</div>
                  <form-errors-wrapper label="Request Type" [control]="propertyFinderForm.controls['requestType']">
                    <ng-select [items]="pfRequestType" bindLabel="name" bindValue="value"
                      placeholder="Select Request Type" class="bg-white" [ngClass]="{'disabled' : editCred}"
                      formControlName="requestType" [virtualScroll]="true">
                    </ng-select>
                  </form-errors-wrapper>
                  <div for="inpSecretKey" class="field-label-req">Secret Key</div>
                  <form-errors-wrapper label="Secret Key" [control]="propertyFinderForm.controls['secretKey']">
                    <input type="text" formControlName="secretKey" name="secretKey" id="inpSecretKey"
                      data-automate-id="inpSecretKey" autocomplete="off" placeholder="enter secret key" />
                  </form-errors-wrapper>
                  <div for="inpApiKey" class="field-label-req">API Key</div>
                  <form-errors-wrapper label="API Key" [control]="propertyFinderForm.controls['apiKey']">
                    <input type="text" formControlName="apiKey" name="apiKey" id="inpApiKey"
                      data-automate-id="inpApiKey" autocomplete="off" placeholder="enter api key" />
                  </form-errors-wrapper>
                </div>
              </div>
              <div class="flex-end p-16 box-shadow-20"> <button type="button" class="btn-gray mr-20"
                  (click)="closeModal()">{{
                  'BUTTONS.cancel' | translate }}</button><button type="button" class="btn-coal" type="button"
                  (click)="saveData()">
                  {{ propertyFinderForm.controls['apiType'].value == 'pull' ? 'Save' :
                  'Add Account' }}
                </button>

              </div>
            </form>
          </div>
          <div *ngSwitchCase="'Bayut'">
            <form [formGroup]="bayutForm" (keydown.enter)="onEnterKey($event)">
              <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
                <div class="box-radio-lg">
                  <div class="field-label header-4 mt-0">Select Option</div>
                  <div class="flex-center mt-10 ip-flex-col">
                    <ng-container *ngIf="!editCred">
                      <input type="radio" class="btn-check" name="apiType" id="push" autocomplete="off"
                        formControlName="apiType" value="push">
                      <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="push">PUSH</label>
                    </ng-container>
                    <input type="radio" class="btn-check" name="apiType" id="pull" autocomplete="off"
                      formControlName="apiType" value="pull">
                    <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="pull">PULL
                    </label>
                  </div>
                </div>
                <div>
                  <div for="inpCFAcc" class="field-label-req">Account Name
                  </div>
                  <form-errors-wrapper label="Account Name" [control]="bayutForm.controls['accountName']">
                    <input type="text" required formControlName="accountName" appDebounceInput [debounceTime]="500"
                      (debounceEvent)="doesAccountNameExists()" name="accountName" id="inpCFAcc"
                      data-automate-id="inpCFAcc" autocomplete="off" placeholder="ex. Manasa Pampana" />
                  </form-errors-wrapper>
                </div>
                <div class="field-label">Login Id/Login Email</div>
                <form-errors-wrapper label="Login Id/Login Email" [control]="bayutForm.controls['loginEmail']">
                  <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
                </form-errors-wrapper>
                <!-- <div class="field-label-req" *ngIf="!editCred || bayutToRecipients()?.controls?.length > 0">Relationship
                  Manager Email</div>
                <form-errors-wrapper label="Relationship Manager Email" [control]="bayutForm?.controls['toRecipients']"
                  [hidden]="editCred">
                  <div class="flex-between position-relative">
                    <input type="text" required placeholder="ex. <EMAIL>" #emailBayutToInput
                      id="emailBayutToInput" class="outline-0 padd-r pr-36" autocomplete="off"
                      [readonly]="editCred ? true: false"
                      (keyup.enter)="addInputField(emailBayutToInput.value, emailBayutToInput, bayutToRecipients())"
                      (input)="handleEmailValidation(bayutForm, emailBayutToInput, 'toRecipients')"
                      [ngClass]="(emailBayutToInput.value && !validateEmail(emailBayutToInput.value)) || (bayutForm.get('toRecipients').touched && !bayutToRecipients().length && !emailBayutToInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailBayutToInput.value && !validateEmail(emailBayutToInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailBayutToInput.value, emailBayutToInput, bayutToRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                  </div>
                  <div *ngIf="emailBayutToInput.value && !validateEmail(emailBayutToInput.value)"
                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                    Enter a valid Email ID.
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="bayutToRecipients()?.controls?.length">
                  <div *ngFor="let email of bayutToRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, bayutToRecipients(), emailBayutToInput)"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || bayutCcRecipients()?.controls?.length > 0">Additional Email
                </div>
                <form-errors-wrapper label="Additional Email" [control]="bayutForm.controls['ccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailBayutCcInput id="emailBayutCcInput"
                      class="outline-0 padd-r pr-36" autocomplete="off" [readonly]="editCred ? true: false"
                      (keyup.enter)="addInputField(emailBayutCcInput.value, emailBayutCcInput, bayutCcRecipients())"
                      [ngClass]="emailBayutCcInput.value && !validateEmail(emailBayutCcInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailBayutCcInput.value && !validateEmail(emailBayutCcInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailBayutCcInput.value, emailBayutCcInput, bayutCcRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailBayutCcInput.value && !validateEmail(emailBayutCcInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="bayutCcRecipients()?.controls?.length">
                  <div *ngFor="let email of bayutCcRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, bayutCcRecipients())"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || bayutBccRecipients()?.controls?.length > 0">Bcc Email</div>
                <form-errors-wrapper label="Bcc Email" [control]="bayutForm.controls['bccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailBayutBccInput id="emailBayutBccInput"
                      class="outline-0 padd-r pr-36" autocomplete="off" [readonly]="editCred ? true: false"
                      (keyup.enter)="addInputField(emailBayutBccInput.value, emailBayutBccInput, bayutBccRecipients())"
                      [ngClass]="emailBayutBccInput?.value && !validateEmail(emailBayutBccInput?.value) ? 'border-red-800': ''" />
                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailBayutBccInput?.value && !validateEmail(emailBayutBccInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailBayutBccInput.value, emailBayutBccInput, bayutBccRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailBayutBccInput.value && !validateEmail(emailBayutBccInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="bayutBccRecipients()?.controls?.length">
                  <div *ngFor="let email of bayutBccRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, bayutBccRecipients())"></span>
                  </div>
                </div> -->
                <div *ngIf="bayutForm.controls['apiType'].value == 'pull'">
                  <div for="inpRequestType" class="field-label-req">Request Type</div>
                  <form-errors-wrapper label="Request Type" [control]="bayutForm.controls['requestType']">
                    <ng-select [items]="requestTypes" placeholder="Select Request Type" class="bg-white"
                      formControlName="requestType" [ngClass]="{'disabled' : editCred}" [virtualScroll]="true">
                    </ng-select>
                  </form-errors-wrapper>
                  <div for="inpApiKey" class="field-label-req">API Key</div>
                  <form-errors-wrapper label="API Key" [control]="bayutForm.controls['apiKey']">
                    <input type="text" formControlName="apiKey" name="apiKey" id="inpApiKey"
                      data-automate-id="inpApiKey" autocomplete="off" placeholder="enter api key" />
                  </form-errors-wrapper>
                </div>
              </div>
              <div class="flex-end p-16 box-shadow-20"> <button type="button" class="btn-gray mr-20"
                  (click)="closeModal()">{{
                  'BUTTONS.cancel' | translate }}</button><button type="button" class="btn-coal"
                  (click)="saveData()">
                  {{ bayutForm.controls['apiType'].value == 'pull' ? 'Save' : 'Add Account'
                  }}
                </button>
              </div>
            </form>
          </div>
          <div *ngSwitchCase="'Dubizzle'">
            <form [formGroup]="dubizzleForm" (keydown.enter)="onEnterKey($event)">
              <div class="px-16 ip-min-w-350 ip-max-w-350 h-100-130 scrollbar">
                <div class="box-radio-lg">
                  <div class="field-label header-4 mt-0">Select Option</div>
                  <div class="flex-center mt-10 ip-flex-col">
                    <ng-container *ngIf="!editCred">
                      <input type="radio" class="btn-check" name="apiType" id="push" autocomplete="off"
                        formControlName="apiType" value="push">
                      <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="push">PUSH</label>
                    </ng-container>
                    <input type="radio" class="btn-check" name="apiType" id="pull" autocomplete="off"
                      formControlName="apiType" value="pull">
                    <label class="btn-outline fw-600 w-100 ip-mt-10 header-3 flex-center" for="pull">PULL
                    </label>
                  </div>
                </div>
                <div>
                  <div for="inpAccountName" class="field-label-req">
                    Account Name
                  </div>
                  <form-errors-wrapper label="Account Name" [control]="dubizzleForm.controls['accountName']">
                    <input type="text" required formControlName="accountName" appDebounceInput [debounceTime]="500"
                      (debounceEvent)="doesAccountNameExists()" name="accountName" id="inpAccountName"
                      data-automate-id="inpAccountName" autocomplete="off" placeholder="ex. Manasa Pampana" />
                  </form-errors-wrapper>
                </div>
                <div class="field-label">Login Id/Login Email</div>
                <form-errors-wrapper label="Login Id/Login Email" [control]="dubizzleForm.controls['loginEmail']">
                  <input type="email" formControlName="loginEmail" placeholder="ex. <EMAIL>">
                </form-errors-wrapper>
                <!-- <div class="field-label-req" *ngIf="!editCred || dubizzleToRecipients()?.controls?.length > 0">
                  Relationship Manager Email</div>
                <form-errors-wrapper label="Relationship Manager Email"
                  [control]="dubizzleForm?.controls['toRecipients']" [hidden]="editCred">
                  <div class="flex-between position-relative">
                    <input type="text" required placeholder="ex. <EMAIL>" #emailDubizzleToInput
                      id="emailDubizzleToInput" class="outline-0 padd-r pr-36" autocomplete="off"
                      [readonly]="editCred ? true: false"
                      (keyup.enter)="addInputField(emailDubizzleToInput.value, emailDubizzleToInput, dubizzleToRecipients())"
                      (input)="handleEmailValidation(dubizzleForm, emailDubizzleToInput, 'toRecipients')"
                      [ngClass]="(emailDubizzleToInput.value && !validateEmail(emailDubizzleToInput.value)) || (dubizzleForm.get('toRecipients').touched && !dubizzleToRecipients().length && !emailDubizzleToInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailDubizzleToInput.value && !validateEmail(emailDubizzleToInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailDubizzleToInput.value, emailDubizzleToInput, dubizzleToRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                  </div>
                  <div *ngIf="emailDubizzleToInput.value && !validateEmail(emailDubizzleToInput.value)"
                    class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                    Enter a valid Email ID.
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="dubizzleToRecipients()?.controls?.length">
                  <div *ngFor="let email of dubizzleToRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, dubizzleToRecipients(), emailDubizzleToInput)"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || dubizzleCcRecipients()?.controls?.length > 0">
                  Additional Email</div>
                <form-errors-wrapper label="Additional Email" [control]="dubizzleForm.controls['ccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailDubizzleCcInput id="emailDubizzleCcInput"
                      class="outline-0 padd-r pr-36" autocomplete="off" [readonly]="editCred ? true: false"
                      (keyup.enter)="addInputField(emailDubizzleCcInput.value, emailDubizzleCcInput, dubizzleCcRecipients())"
                      [ngClass]="emailDubizzleCcInput.value && !validateEmail(emailDubizzleCcInput.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailDubizzleCcInput.value && !validateEmail(emailDubizzleCcInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailDubizzleCcInput.value, emailDubizzleCcInput, dubizzleCcRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailDubizzleCcInput.value && !validateEmail(emailDubizzleCcInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="dubizzleCcRecipients()?.controls?.length">
                  <div *ngFor="let email of dubizzleCcRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, dubizzleCcRecipients())"></span>
                  </div>
                </div>
                <div class="field-label" *ngIf="!editCred || dubizzleBccRecipients()?.controls?.length > 0">Bcc Email
                </div>
                <form-errors-wrapper label="Bcc Email" [control]="dubizzleForm.controls['bccRecipients']"
                  *ngIf="!editCred">
                  <div class="flex-between position-relative">
                    <input type="text" placeholder="ex. <EMAIL>" #emailDubizzleBccInput id="emailDubizzleBccInput"
                      class="outline-0 padd-r pr-36" autocomplete="off" [readonly]="editCred ? true: false"
                      (keyup.enter)="addInputField(emailDubizzleBccInput.value, emailDubizzleBccInput, dubizzleBccRecipients())"
                      [ngClass]="emailDubizzleBccInput?.value && !validateEmail(emailDubizzleBccInput?.value) ? 'border-red-800': ''" />

                    <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md"
                      [ngClass]="emailDubizzleBccInput?.value && !validateEmail(emailDubizzleBccInput.value) ? 'pe-none ': 'cursor-pointer'"
                      (click)="addInputField(emailDubizzleBccInput.value, emailDubizzleBccInput, dubizzleBccRecipients())">
                      <span class="icon ic-plus ic-x-xs"></span>
                    </div>
                    <div *ngIf="emailDubizzleBccInput.value && !validateEmail(emailDubizzleBccInput.value)"
                      class="position-absolute right-20 nbottom-15 fw-semi-bold text-xxs text-wrap text-red">
                      Enter a valid Email ID.
                    </div>
                  </div>
                </form-errors-wrapper>
                <div class="d-flex flex-wrap mt-12" *ngIf="dubizzleBccRecipients()?.controls?.length">
                  <div *ngFor="let email of dubizzleBccRecipients()?.controls; let i = index"
                    class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                      email.value }}</span>
                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover" *ngIf="!editCred"
                      (click)="removeEmail(i, dubizzleBccRecipients())"></span>
                  </div>
                </div> -->
                <div *ngIf="dubizzleForm.controls['apiType'].value == 'pull'">
                  <div for="inpRequestType" class="field-label-req">Request Type</div>
                  <form-errors-wrapper label="Request Type" [control]="dubizzleForm.controls['requestType']">
                    <ng-select [items]="requestTypes" placeholder="Select Request Type" class="bg-white"
                      formControlName="requestType" [ngClass]="{'disabled' : editCred}" [virtualScroll]="true">
                    </ng-select>
                  </form-errors-wrapper>
                  <div for="inpApiKey" class="field-label-req">API Key</div>
                  <form-errors-wrapper label="API Key" [control]="dubizzleForm.controls['apiKey']">
                    <input type="text" formControlName="apiKey" name="apiKey" id="inpApiKey"
                      data-automate-id="inpApiKey" autocomplete="off" placeholder="enter api key" />
                  </form-errors-wrapper>
                </div>
              </div>
              <div class="flex-end p-16 box-shadow-20"> <button type="button" class="btn-gray mr-20"
                  (click)="closeModal()">{{
                  'BUTTONS.cancel' | translate }}</button><button type="button" class="btn-coal"
                  (click)="saveData()">
                  {{ dubizzleForm.controls['apiType'].value == 'pull' ? 'Save' :
                  'Add Account'
                  }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <!-- details -->
  <ng-template #addAndListingPage>
    <div class="px-16">
      <!-- <div class="ph-w-100-60"> -->
      <div class="align-center px-10 border flex-grow-1 no-validation bg-white">
        <ng-container>
          <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
          <input (keydown)="onSearch($event)" (input)="isEmptyInput($event)" placeholder="type to search"
            autocomplete="off" [(ngModel)]="searchTerm" name="search" class="border-0 outline-0 w-100 py-12">
          <small class="text-muted text-nowrap ph-d-none">
            ({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </ng-container>
        <div class="show-dropdown-white align-center position-relative ip-br-0">
          <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
              {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
          <ng-select [items]="showEntriesSize" [formControl]="pageEntry" (change)="assignPageSize()"
            [virtualScroll]="true" [placeholder]="PageSize" class="w-150 tb-w-120px" [searchable]="false">
          </ng-select>
        </div>
      </div>
      <div class="bg-white py-6 w-100-170 tb-w-100-30 scrollbar table-scrollbar">
        <table class="table standard-table no-vertical-border"
          *ngIf="updatedIntegrationList?.length > 0 || !searchTerm">
          <thead>
            <tr class="w-100 text-nowrap">
              <th class="w-40" *ngIf="canBulkAssignment || canBulkReassign">
                <div class="mb-16 ml-0">
                  <label class="checkbox-container"> <input type="checkbox" [checked]="isAllSelected()"
                      (change)="selectAllRows($event)">
                    <span class="checkmark"></span>
                  </label>
                </div>
              </th>
              <th class="w-130">Account Name</th>
              <th class="w-70px">API Type</th>
              <th *ngIf="displayName === 'Bayut' || displayName === 'Dubizzle' || displayName === 'Property Finder'"
                class="w-100px">Request Type</th>
              <th class="w-100px">{{ 'INTEGRATION.leads-count' | translate }}</th>
              <th class="w-130">Integration Status</th>
              <th class="w-180" *ngIf="displayName === 'Common Floor'|| displayName === 'Just Lead'">Relationship
                Manager Email(s)</th>
              <th class="w-130" *ngIf="displayName === 'Common Floor' ||  displayName === 'Just Lead'">Additional
                Email(s) </th>
              <th class="w-100px" *ngIf="displayName === 'Common Floor' ||  displayName === 'Just Lead'">Bcc Email(s)
              </th>
              <th
                [ngClass]="displayName === 'Property Finder' || displayName === 'Dubizzle' || displayName === 'Bayut' ? 'w-180' : 'w-160'">
                {{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-280 scrollbar">
            <ng-container *ngFor="let integration of updatedIntegrationList">
              <tr>
                <td class="w-40" *ngIf="canBulkReassign || canBulkAssignment">
                  <div class="mb-16">
                    <label class="checkbox-container">
                      <input type="checkbox" [ngModelOptions]="{standalone: true}" (change)="onCheckboxChange($event)"
                        [(ngModel)]="integration.isSelected" /><span class="checkmark"></span>
                    </label>
                  </div>
                </td>
                <td [title]="integration?.accountName" class="w-130">
                  <div class="text-truncate-1 break-all">{{ integration.accountName }}</div>
                </td>
                <td class="w-70px">
                  <div [ngClass]="integration.credentials ? 'text-accent-red' : 'text-accent-green'">
                    {{integration.credentials ? 'PULL' : 'PUSH'}}</div>
                </td>
                <td *ngIf="displayName === 'Bayut' || displayName === 'Dubizzle' || displayName === 'Property Finder'"
                  class="w-100px">
                  <div>{{ getRequestTypeDisplay(integration?.credentials?.type) || '--'}}
                  </div>
                </td>
                <td class="w-100px">
                  <div>{{integration?.totalLeadCount}} ({{ integration?.leadCount }})</div>
                </td>
                <td class="w-130 fw-600"
                  [ngClass]="integration?.status == 'InComplete' ? 'text-accent-red': 'text-accent-green'">
                  {{integration?.status}}</td>
                <td class="w-180" *ngIf="displayName === 'Common Floor' ||  displayName === 'Just Lead'">
                  <div class="text-truncate-1 break-all" [title]="integration?.toRecipients">
                    {{integration?.toRecipients}}</div>
                </td>
                <td class="w-130" *ngIf="displayName === 'Common Floor' ||  displayName === 'Just Lead'">
                  <div class="text-truncate-1 break-all" [title]="integration?.ccRecipients">
                    {{integration?.ccRecipients}}</div>
                </td>
                <td class="w-100px" *ngIf="displayName === 'Common Floor' ||  displayName === 'Just Lead'">
                  <div class="text-truncate-1 break-all" [title]="integration?.bccRecipients">
                    {{integration?.bccRecipients}}</div>
                </td>
                <td
                  [ngClass]="displayName === 'Property Finder' || displayName === 'Dubizzle' || displayName === 'Bayut' ? 'w-180' : 'w-160'">
                  <div class="align-center">
                    <div *ngIf="integration.credentials">
                      <div title="Edit" class="bg-accent-green icon-badge" (click)="editData(addAccount,integration)">
                        <span class="icon ic-pen ic-xxs"></span>
                      </div>
                    </div>
                    <div *ngIf="!integration.credentials">
                      <div title="Download" class="btn-accent-green icon-badge"
                        (click)="reDownloadExcel(integration.accountId)">
                        <span class="icon ic-download ic-xxxs"></span>
                      </div>
                    </div>
                    <div *ngIf="canDelete">
                      <div title="Delete" class="bg-light-red icon-badge"
                        (click)="initDeleteIntegration(integration.accountId, integration.accountName)">
                        <span class="icon ic-delete ic-xxxs"></span>
                      </div>
                    </div>
                    <div title="Assign To" *ngIf="canAssign && canViewForFilter" class="bg-blue-800 icon-badge"
                      (click)="openAssignmentModal(integration);">
                      <span class="icon ic-assign-to ic-xxxs"></span>
                    </div>
                    <div *ngIf="displayName != 'Facebook'" title="Project & Location" class="bg-violet icon-badge"
                      (click)="openProjectAndLocationModal(ProjectAndLocationModal,
                                                integration?.accountId, integration?.leadSource, integration.accountName);">
                      <span class="icon ic-building-secondary ic-xxxs"></span>
                    </div>
                    <div *ngIf="displayName != 'Facebook'" title="Country Code" class="bg-accent-green icon-badge"
                      (click)="openCountryCode( CountryCodeModal,
                                                integration?.accountId, integration?.leadSource, integration.accountName);">
                      <span class="icon ic-call-sms ic-xxxs"></span>
                    </div>
                    <div
                      *ngIf="(displayName === 'Property Finder' || displayName === 'Dubizzle' || displayName === 'Bayut') && integration?.endPointUrl"
                      title="CopyUrl" class="bg-brown icon-badge" (click)="copyUrl(integration?.endPointUrl)">
                      <span class="icon ic-copy-clipboard ic-xxxs"></span>
                    </div>
                  </div>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="mt-16 flex-end" *ngIf="totalCount">
        <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*PageSize + 1}}
          {{ 'GLOBAL.to-small' | translate }} {{currOffset*PageSize + rowData?.length}}
          {{ 'GLOBAL.of-small' | translate }} {{totalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
        <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalCount,PageSize)'
          (pageChange)="onPageChange($event)">
        </pagination>
      </div>
      <div *ngIf="searchTerm && updatedIntegrationList?.length === 0" class="flex-center h-100-250 min-h-250">
        <div class="flex-center-col">
          <img src="assets/images/layered-cards.svg" alt="No lead found">
          <div class="header-3 fw-600 text-center">No Acount Found </div>
        </div>
      </div>
    </div>
  </ng-template>
  <!-- bulk -->
  <div class="justify-center">
    <ng-container>
      <div class="position-absolute bg-white bottom-12 br-12 ph-flex-col flex-between shadow-sm p-10 z-index-2"
        [ngClass]="{'d-none': selectedCount === 0}">
        <div class="align-center scrollbar ip-max-w-100-130 scroll-hide">
          <div class="align-center">
            <div class="fw-600 text-coal mr-20  text-nowrap">{{ selectedCount }}
              {{ selectedCount > 1 ? 'Items' : 'Item' }} {{ 'LEADS.selected' | translate}}
            </div>
            <div class="flex-center">
              <button type="button" *ngIf="canBulkReassign" class="btn-bulk" id="btnBulkReassign"
                data-automate-id="btnBulkReassign" (click)="openBulkReassignModel()">Bulk
                Reassign</button>
              <button type="button" class="btn-bulk" (click)="openBulkProjectLocationModel(ProjectAndLocationModal)"
                *ngIf="canBulkAssignment">
                {{'LEADS.bulk'| translate}} Project & Location</button>
              <button type="button" class="btn-bulk" (click)="openBulkCountryCodeModal(CountryCodeModal)"
                *ngIf="canBulkAssignment">Bulk
                Country Code</button>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
  <!-- project &location -->
  <ng-template #ProjectAndLocationModal>
    <ng-container *ngIf="isShowProjectAndLocationModal">
      <div class="p-16 min-w-350 max-w-350 h-100-60 scrollbar">
        <img [type]="'leadrat'" [appImage]="image" alt="img" />
        <div class="align-center mt-30">
          <span class="icon ic-chevron-left ic-xxs ic-black cursor-pointer mr-10"
            (click)="isShowProjectAndLocationModal = false; isBulkAssignModel=true; modalService.hide()"></span>
          <h3 class="fw-700">{{ 'LEADS.assignment' | translate }}</h3>
        </div>
        <div class="bg-light-pearl mt-20 br-6 flex-between break-all bg-profile">
          <div class="flex-column pt-20 pl-10 pb-20">
            <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel" class="fw-semi-bold fv-sm-caps">
              Account Name(s)
            </div>
            <div *ngIf="isBulkAssignModel" class="fw-semi-bold fv-sm-caps">
              Account Name
            </div>
            <div *ngIf="selectedIntegrations?.length > 0 && !isBulkAssignModel">
              <span class="fw-700 text-small" *ngFor="let name of selectedIntegrations; let last = last">
                {{ name.accountName }}{{ !last ? ', ' : ' ' }}
              </span>
            </div>
            <div class="fw-700 text-small" *ngIf="isBulkAssignModel">{{ selectedAccountName
              }}</div>
          </div>
        </div>
        <div class="field-label fw-semi-bold"> {{'PROJECTS.project' | translate}}
        </div>
        <ng-select [virtualScroll]="true" [items]="allProjectList" class="bg-white" bindLabel="name" bindValue="id"
          ResizableDropdown placeholder="ex. ABC project" [formControl]="project"></ng-select>
        <div class="field-label fw-semi-bold"> {{'LOCATION.location' | translate}}
        </div>
        <ng-select [virtualScroll]="true" [items]="placesList" class="bg-white" bindLabel="location" bindValue="id"
          ResizableDropdown placeholder="ex. ABC Location" [formControl]="location"></ng-select>
      </div>
      <div class="flex-end p-16 box-shadow-20">
        <button type="button" class="btn-gray mr-20"
          (click)="isShowProjectAndLocationModal = false;isBulkAssignModel=true; modalService.hide()">
          {{ 'BUTTONS.cancel' | translate }}</button>
        <button type="button" class="btn-coal" (click)="updateProjectAndLocation()">
          {{ 'BUTTONS.save' | translate }}</button>
      </div>
    </ng-container>
  </ng-template>
  <!-- country -->
  <ng-template #CountryCodeModal>
    <ng-container *ngIf="isShowCountryCodeModal">
      <country-code [image]="image" [isBulkAssignModel]="isBulkAssignModel" [selectedAccountName]="selectedAccountName"
        [selectedIntegrations]="selectedIntegrations" [selectedAccountId]="selectedAccountId"
        [selectedCount]="selectedCount" [updatedIntegrationList]="updatedIntegrationList" [displayName]="displayName"
        (isShowCountryCodeModalChanged)="isShowCountryCodeModal = $event; selectedCountReset();"></country-code>

    </ng-container>
  </ng-template>
</form>
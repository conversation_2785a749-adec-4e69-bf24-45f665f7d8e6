<div class="w-100 d-flex tb-flex-col mt-16 position-relative z-index-2">
    <ng-container *ngIf="projectInfo?.images?.length else noImage">
        <div class="max-w-590 min-w-590 w-590 mr-10 mb-20 tb-max-w-unset tb-min-w-unset tb-w-100">
            <div class="flex-center position-relative tb-mr-10">
                <img [type]="'leadrat'"
                    [appImage]="projectInfo?.images[0].imageFilePath?.includes(s3BucketUrl)?projectInfo?.images[0].imageFilePath: s3BucketUrl+projectInfo?.images[0].imageFilePath"
                    alt="" class="h-300 position-relative w-100 br-4 obj-cover"
                    (click)="showImages(expandImage, projectInfo?.images[0])">
                <!-- <div class="bg-blur position-absolute flex-center cursor-pointer w-100 br-4">
                    <img [type]="'leadrat'"
                        [appImage]="projectInfo?.images[0].imageFilePath?.includes(s3BucketUrl)?projectInfo?.images[0].imageFilePath:s3BucketUrl+projectInfo?.images[0].imageFilePath"
                        alt="" class="h-300 scrollbar" (click)="showImages(expandImage, projectInfo?.images[0])">
                </div> -->
            </div>
            <span (click)="isShareVisible = !isShareVisible"
                class="icon ic-share ic-lg position-absolute p-20 br-8 bg-black-50 cursor-pointer top-12 left-10 z-index-1001"></span>
            <ng-container *ngIf="isShareVisible">
                <div class="mask mt-30 ml-12 z-index-1001">
                    <span class="icon ic-whatsapp ic-lg position-absolute p-20 br-8 bg-black-50 cursor-pointer"
                        (click)="shareInfo('whatsApp')"></span>
                    <span class="icon ic-envelope ic-lg position-absolute p-20 br-8 bg-black-50 cursor-pointer mt-50"
                        (click)="shareInfo('email')"></span>
                </div>
            </ng-container>
        </div>
        <div
            class="max-w-590 min-w-590 w-590 mr-10 d-flex flex-wrap position-relative tb-max-w-unset tb-min-w-unset tb-w-100">
            <ng-container *ngIf="projectInfo?.images?.length === 1">
                <img src="../../../../assets/images/no-images.svg" alt="" class="obj-cover w-100 h-300 br-4">
            </ng-container>
            <div class="d-flex flex-wrap w-100" *ngIf="projectInfo?.images?.length >= 1">
                <ng-container *ngFor="let image of projectInfo?.images.slice(1, 5); let i = index">
                    <div (click)="showImages(expandImage, image)"
                        [ngClass]="{'w-100per': projectInfo?.images?.length === 2, 'w-100 flex-wrap': projectInfo?.images?.length === 3, 'w-50': projectInfo?.images?.length >= 4}">
                        <div class="mr-10 mb-10">
                            <div class="position-relative flex-center">
                                <img [type]="'leadrat'"
                                    [appImage]="image.imageFilePath?.includes(s3BucketUrl)?image.imageFilePath:s3BucketUrl+image.imageFilePath"
                                    alt="" class="w-100 br-4 obj-cover"
                                        [ngClass]="{'h-300': projectInfo?.images?.length === 2, 'h-140': projectInfo?.images?.length >= 3, 'blur-6': (i === 3 && projectInfo?.images?.length >= 6) }">
                                <!-- <div class="bg-blur flex-center cursor-pointer w-100 position-absolute br-4">
                                    <img [type]="'leadrat'"
                                        [appImage]="image.imageFilePath?.includes(s3BucketUrl)?image.imageFilePath:s3BucketUrl+image.imageFilePath"
                                        alt="" class="scrollbar"
                                        [ngClass]="{'h-300': projectInfo?.images?.length === 2, 'h-140': projectInfo?.images?.length >= 3, 'blur-6': (i === 3 && projectInfo?.images?.length >= 6) }">
                                </div> -->
                            </div>
                        </div>
                    </div>
                </ng-container>
                <div *ngIf="projectInfo?.images?.length > 5"
                    class="position-absolute right-100 bottom-80 cursor-pointer"
                    (click)="showImages(expandImage, projectInfo?.images[4])">
                    <div class="align-center text-white">
                        <div class="icon ic-large  ic-image mr-6"></div>
                        {{ projectInfo?.images.length - 5 }} more
                    </div>
                </div>
            </div>

        </div>
    </ng-container>
    <ng-template #noImage>
        <div class="mb-10">
            <img src="../../../../assets/images/no-images.svg" alt="" class="obj-cover w-100 h-300 br-4">
        </div>
    </ng-template>
</div>
<ng-template #expandImage>
    <div class="m-auto max-w-1260 min-w-1260 position-relative tb-min-w-unset tb-max-w-unset tb-w-100">
        <div class="px-40 ip-px-4">
            <div class="icon ic-close-secondary ic-large cursor-pointer ntop-20 right-0 ph-right-20 position-absolute"
                (click)="imagesModalRef.hide();"></div>
            <div class="flex-between ip-flex-between-unset ip-flex-col br-4 p-16">
                <div>
                    <h3 class="text-accent-green fw-600 text-truncate-1 break-all max-w-200" *ngIf="projectInfo?.name">
                        “{{projectInfo?.name}}”</h3>
                    <h4 class="text-white fw-semi-bold mt-4 ml-10" *ngIf="projectInfo?.projectType?.displayName">
                        {{projectInfo?.projectType?.displayName}}</h4>
                    <div class="align-center mr-12 mt-4" *ngIf="projectInfo?.address">
                        <div class="dot dot-lg bg-ash mr-6">
                            <div class="icon ic-xxs ic-location-circle ic-coal"></div>
                        </div>
                        <div class="fw-600 text-sm text-white">{{getLocationDetailsByObj(projectInfo?.address)}}
                        </div>
                    </div>
                </div>
                <div class="text-white ip-mt-10">
                    <h6 *ngIf="projectInfo?.minimumPrice || projectInfo?.maximumPrice">Price</h6>
                    <div class="align-center mt-4">
                        <ng-container *ngIf="projectInfo?.minimumPrice">
                            <h4 class="fw-700">{{projectInfo?.minimumPrice? formatBudget(projectInfo?.minimumPrice,
                                projectInfo?.monetaryInfo?.currency): ''}}</h4>
                        </ng-container>
                        <div class="mx-6" *ngIf="projectInfo?.minimumPrice && projectInfo?.maximumPrice">-</div>
                        <ng-container *ngIf="projectInfo?.maximumPrice">
                            <h4 class="fw-700">{{ projectInfo?.maximumPrice? formatBudget(projectInfo?.maximumPrice,
                                projectInfo?.monetaryInfo?.currency): ''}}</h4>
                        </ng-container>
                    </div>
                    <div [ngClass]="!globalSettingsDetails?.shouldEnableEnquiryForm ? 'd-none': ''"
                        class="br-20 py-8 d-flex fw-semi-bold text-sm pr-16 pl-30 mt-10 w-115 cursor-pointer position-relative bg-white text-black-200"
                        (click)="selectImage(imageArray[imageArray.length-1])"> <ng-lottie [options]='tick'
                            height='20px' width="20px" class="position-absolute left-6 bottom-4"></ng-lottie>contact
                        seller</div>
                </div>
            </div>
            <div class="mb-20 position-relative">
                <div *ngIf="selectedImage.imageFilePath !== '../../../../assets/images/enquired-form.svg' else EQ">
                    <div class="flex-center position-relative">
                        <img [appImage]="selectedImage?.imageFilePath?.includes(s3BucketUrl)?selectedImage?.imageFilePath:s3BucketUrl+selectedImage?.imageFilePath"
                            [type]="'project'" alt="project image" class="w-100 position-relative h-500">
                        <div class="w-100 bg-blur position-absolute flex-center br-4">
                            <img [appImage]="selectedImage?.imageFilePath?.includes(s3BucketUrl)?selectedImage?.imageFilePath:s3BucketUrl+selectedImage?.imageFilePath"
                                [type]="'project'" alt="project image" class="h-500 scrollbar ph-w-100">
                        </div>
                    </div>
                </div>
                <ng-template #EQ>
                    <div class="w-100 bg-blur flex-center br-4">
                        <project-enquiry-form [projectUnit]="projectUnit"
                            [projectInfo]="projectInfo"></project-enquiry-form>
                    </div>
                </ng-template>
                <div class="position-absolute left-40 top-230 cursor-pointer ph-left-0" (click)="previous()">
                    <div class="dot dot-xl bg-black opacity-5"> </div><span
                        class="icon ic-xxxs  ic-arrow-left position-absolute top-10 left-10"></span>
                </div>
                <div class="position-absolute right-40 top-230 cursor-pointer ph-right-0" (click)="next()">
                    <div class="dot dot-xl bg-black opacity-5"> </div><span
                        class="icon ic-xxxs  ic-arrow-right position-absolute top-10 left-10"></span>
                </div>
            </div>
            <drag-scroll
                class="d-flex scrollbar scroll-hide position-relative mx-40 ip-mx-0 tb-w-100-200 ip-w-100-96 ph-w-100-30">
                <div class="d-flex scroll-behaviour scrollbar scroll-hide position-relative">
                    <div *ngFor="let image of imageArray; let i = index" class="w-100">
                        <div class="w-250" (click)="selectImage(image)">
                            <div class="mr-30 mb-10 ip-mr-10">
                                <img [type]="'leadrat'"
                                    [appImage]="!globalSettingsDetails?.shouldEnableEnquiryForm ? (image.imageFilePath?.includes(s3BucketUrl) ? image.imageFilePath : s3BucketUrl + image.imageFilePath)
                                      : (i === imageArray.length - 1 || image.imageFilePath?.includes(s3BucketUrl) ? image.imageFilePath : s3BucketUrl + image.imageFilePath)"
                                    alt="" class="w-100 h-140 br-4 cursor-pointer obj-cover"
                                    [ngClass]="{'border-accent-green-4': image === selectedImage}">
                            </div>
                        </div>
                    </div>
                </div>
            </drag-scroll>
        </div>
    </div>
</ng-template>
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { IdlePreload } from 'angular-idle-preload';

import { AuthGuard } from 'src/app/core/guards/auth.guard';
import { NoAuthGuard } from 'src/app/core/guards/no-auth.guard';
import { PermissionsGuard } from 'src/app/core/guards/permissions.guard';
import { LoginComponent } from 'src/app/features/login/login.component';
import { HeaderComponent } from 'src/app/layout/header/header.component';
import { LeftNavComponent } from 'src/app/layout/left-nav/left-nav.component';

const routes: Routes = [
  { path: '', redirectTo: 'leads', pathMatch: 'full' },
  {
    path: 'dashboard',
    loadChildren: () =>
      import('./features/dashboard/dashboard.module').then(
        (m) => m.DashboardModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'global-config',
    loadChildren: () =>
      import('./features/global-config/global-config.module').then(
        (m) => m.GlobalConfigModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'task',
    loadChildren: () =>
      import('./features/task/task.module').then((m) => m.TaskModule),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'leads',
    loadChildren: () =>
      import('./features/leads/leads.module').then((m) => m.LeadsModule),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'properties',
    loadChildren: () =>
      import('./features/property/property.module').then(
        (m) => m.PropertyModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'teams',
    loadChildren: () =>
      import('./features/teams/teams.module').then((m) => m.TeamsModule),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'profile',
    loadChildren: () =>
      import('./features/profile/profile.module').then((m) => m.ProfileModule),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'login',
    loadChildren: () =>
      import('./features/login/login.module').then((m) => m.LoginModule),
    data: { hideAppLayout: true },
    canActivate: [NoAuthGuard],
  },
  {
    path: 'reports',
    loadChildren: () =>
      import('./features/reports/reports.module').then((m) => m.reportModule),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'attendance',
    loadChildren: () =>
      import('./features/attendance/attendance.module').then(
        (m) => m.attendanceModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'projects',
    loadChildren: () =>
      import('./features/projects/projects.module').then(
        (m) => m.ProjectsModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'data',
    loadChildren: () =>
      import('./features/data-management/data.module').then(
        (m) => m.dataModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'external',
    loadChildren: () =>
      import('./features/no-auth/no-auth.module').then((m) => m.NoAuthModule),
    data: { hideAppLayout: true },
  },
  {
    path: 'invoice',
    loadChildren: () =>
      import('./features/invoice/invoice.module').then((m) => m.InvoiceModule),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'whatsApp',
    loadChildren: () =>
      import('./features/whatsApp/whatsApp.module').then(
        (m) => m.whatsAppModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
  {
    path: 'engage-to',
    loadChildren: () =>
      import('./features/engage-to/engage-to.module').then(
        (m) => m.EngageToModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
    {
    path: 'this-applications',
    loadChildren: () =>
      import('./features/this-applications/this-applications.module').then(
        (m) => m.ThisApplicationsModule
      ),
    canActivate: [AuthGuard, PermissionsGuard],
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      useHash: false,
      preloadingStrategy: IdlePreload,
      enableTracing: false,
      scrollPositionRestoration: 'enabled',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule { }

export const APP_DECLARATIONS = [
  LoginComponent,
  LeftNavComponent,
  HeaderComponent
];

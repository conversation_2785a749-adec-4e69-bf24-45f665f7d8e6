import { Component, EventEmitter, Input, OnInit, Output, TemplateRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil,skipWhile } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { formatBudget, getLocationDetailsByObj } from 'src/app/core/utils/common.util';
import { platformShareLinks } from 'src/app/core/utils/share.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'project-images',
  templateUrl: './project-images.component.html'
})

export class ProjectImagesComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  tick: AnimationOptions = {
    path: 'assets/animations/circle-white-tick.json',
  };
  selectedSection: string = 'Overview';
  selectedImage: any;
  selectedImageIndex = 0;
  imageArray: any[];
  isShareVisible: boolean = false;
  @Input() projectInfo: any;
  @Input() projectUnit: any;
  @Output() selectedSectionChange = new EventEmitter<string>();
  s3BucketUrl = env.s3ImageBucketURL;
  getLocationDetailsByObj = getLocationDetailsByObj;

  formatBudget = formatBudget;
  globalSettingsDetails: any;

  constructor(private modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    private imagesModalRef: BsModalRef) { }

  ngOnInit(): void {
    this.sortImages();
    this.selectedImage = this.projectInfo?.images.length > 0 ? this.projectInfo?.images[0] : null;
    if (this.projectInfo && Array.isArray(this.projectInfo.images)) {
      this.imageArray = [
        ...this.projectInfo.images?.filter((img:any)=>img),
        {
          "imageFilePath": "../../../../assets/images/enquired-form.svg",
        }
      ];
      
    }

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper),skipWhile((data: any) => !Object.keys(data || {}).length))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        if (!this.globalSettingsDetails?.shouldEnableEnquiryForm) {
          this.imageArray = this.imageArray.slice(0, -1);
        }
      });
  }

  sortImages(): void {
    const sortedImages = [...this.projectInfo.images];

    sortedImages.sort((a, b) => {
      if (a.isCoverImage && !b.isCoverImage) {
        return -1;
      } else if (!a.isCoverImage && b.isCoverImage) {
        return 1;
      } else {
        return 0;
      }
    });

    this.projectInfo.images = sortedImages;
  }


  showImages(images: TemplateRef<any>, imageUrl: number) {
    this.selectedImage = imageUrl;
    let initialState: any = {
      class: 'modal-dialog no-bg',
    };
    this.imagesModalRef = this.modalService.show(images, initialState);
  }

  selectImage(image: number) {
    this.selectedImage = image;
  }

  previous(): void {
    this.selectedImageIndex--;
    if (this.selectedImageIndex < 0) {
      this.selectedImageIndex = this.imageArray.length - 1;
    }
    this.selectedImage = this.imageArray[this.selectedImageIndex];

    this.scroll('previous');
  }

  next(): void {
    this.selectedImageIndex++;
    if (this.selectedImageIndex >= this.imageArray.length) {
      this.selectedImageIndex = 0;
    }
    this.selectedImage = this.imageArray[this.selectedImageIndex];

    this.scroll('next');
  }

  scroll(direction: 'next' | 'previous'): void {
    const container = document.querySelector('.scroll-behaviour') as HTMLElement;
    let scrollAmount = 250;

    if (this.selectedImageIndex < 0 || this.selectedImageIndex >= this.imageArray.length) {
      return;
    }

    if (direction === 'previous') {
      scrollAmount = -scrollAmount;
    }

    if (direction === 'next' && this.selectedImageIndex === 0) {
      scrollAmount *= -1;
    }

    container.scrollTo({
      left: container.scrollLeft + scrollAmount,
      behavior: 'smooth'
    });
  }


  shareInfo(via?: string) {
    switch (via) {
      case 'email':
        window.location.href = platformShareLinks(
          'email',
          location.href,
          '',
          ''
        );
        break;

      case 'whatsApp':
        window.open(
          platformShareLinks('whatsApp', location.href, '', ''),
          '_blank'
        );
        break;
    }
  }
}
